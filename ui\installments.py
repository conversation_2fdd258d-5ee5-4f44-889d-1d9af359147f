from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QLineEdit, QTableWidget, QTableWidgetItem,
                            QFormLayout, QTextEdit, QHeaderView, QMessageBox,
                            QDialog, QComboBox, QDateEdit, QDoubleSpinBox,
                            QFileDialog, QMenu, QAction, QSizePolicy, QFrame,
                            QTextBrowser, QGridLayout, QScrollArea)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QFont, QColor, QPainter, QTextDocument, QPixmap, QBrush, QPen, QIcon, QLinearGradient
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

from database import Installment, InstallmentItem, Client
from utils import (show_error_message, show_info_message, show_confirmation_message,
                    qdate_to_datetime, datetime_to_qdate, format_currency,
                    generate_installment_number, format_quantity)
import datetime
import re

from ui.unified_styles import (UnifiedStyles, StyledButton, StyledGroupBox,
                                StyledTable, StyledLabel)
from ui.common_dialogs import WarningDialog
from ui.title_bar_utils import TitleBarStyler
from ui.multi_selection_mixin import MultiSelectionMixin
from sqlalchemy import func


class InstallmentsWidget(QWidget, MultiSelectionMixin):
    """واجهة إدارة الأقساط مع التحديد المتعدد - مطابقة للفواتير"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.selected_items = []
        self.init_ui()

    def safe_call_method(self, method_name):
        """استدعاء آمن للدوال مع فحص وجودها"""
        try:
            if hasattr(self, method_name):
                method = getattr(self, method_name)
                if callable(method):
                    method()
                    return True

            # إذا لم توجد الدالة، عرض رسالة
            show_info_message("قريباً", f"ميزة {method_name} قيد التطوير")
            return False

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تنفيذ العملية: {str(e)}")
            return False

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق للفواتير
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش من 10 إلى 2
        main_layout.setSpacing(2)  # تقليل المسافات من 8 إلى 2

        # إنشاء العنوان الرئيسي مطابق للفواتير
        self.create_main_title(main_layout)

        # إنشاء شريط البحث مطابق للفواتير
        self.create_search_section(main_layout)

        # إنشاء الجدول مطابق للفواتير
        self.create_table_section(main_layout)

        # إنشاء شريط الأزرار مطابق للفواتير
        self.create_buttons_section(main_layout)

        # تعيين التخطيط للنافذة
        self.setLayout(main_layout)

        # تحميل البيانات
        self.load_data()

    def create_main_title(self, layout):
        """إنشاء العنوان الرئيسي مطابق للفواتير"""
        # إضافة العنوان الرئيسي المطور والمحسن مطابق للفواتير
        title_label = QLabel("🏦 إدارة الأقساط المتطورة - نظام شامل ومتقدم لإدارة الأقساط مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        layout.addWidget(title_label)

    def create_search_section(self, layout):
        """إنشاء قسم البحث مطابق للفواتير"""
        # إنشاء إطار علوي محسن بنفس الأسلوب القديم (صف واحد) مطابق للفواتير
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 65px;
                min-height: 60px;
            }
        """)

        # تخطيط أفقي واحد محسن (الطريقة القديمة)
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        search_layout.setSpacing(4)  # مسافات متوازنة

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        top_container = QVBoxLayout()
        top_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        top_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        top_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        top_container.addLayout(search_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        top_container.addStretch(1)

        # تسمية البحث محسنة مع ألوان موحدة مطابقة للفواتير
        search_label = QLabel("🔍 بحث:")
        search_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 70px;
                max-width: 70px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 4px solid rgba(96, 165, 250, 0.95);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        search_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("🔎 ابحث برقم القسط، العميل أو الملاحظات...")
        self.search_edit.textChanged.connect(self.filter_installments)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.9),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.9),
                    stop:0.6 rgba(248, 250, 252, 0.95),
                    stop:0.8 rgba(255, 255, 255, 0.9),
                    stop:1 rgba(226, 232, 240, 0.85));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                padding: 8px 15px;
                font-size: 16px;
                font-weight: 900;
                color: #1f2937;
                max-height: 38px;
                min-height: 34px;
                selection-background-color: rgba(96, 165, 250, 0.3);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
            }
        """)



        # تسمية التصفية مطورة بألوان احترافية
        filter_label = QLabel("🎯 حالة:")
        filter_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 16px;
                font-weight: 900;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(37, 99, 235, 0.8),
                    stop:0.2 rgba(59, 130, 246, 0.7),
                    stop:0.4 rgba(96, 165, 250, 0.6),
                    stop:0.6 rgba(139, 92, 246, 0.7),
                    stop:0.8 rgba(124, 58, 237, 0.8),
                    stop:1 rgba(109, 40, 217, 0.7));
                border: 3px solid rgba(96, 165, 250, 0.8);
                border-radius: 15px;
                min-width: 65px;
                max-width: 65px;
                max-height: 38px;
                min-height: 34px;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                transition: all 0.3s ease;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
            QLabel:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(59, 130, 246, 0.9),
                    stop:0.2 rgba(96, 165, 250, 0.8),
                    stop:0.4 rgba(139, 92, 246, 0.7),
                    stop:0.6 rgba(124, 58, 237, 0.8),
                    stop:0.8 rgba(109, 40, 217, 0.9),
                    stop:1 rgba(91, 33, 182, 0.8));
                border: 3px solid rgba(96, 165, 250, 0.9);
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25);
            }
        """)
        filter_label.setAlignment(Qt.AlignCenter)  # توسيط النص داخل التسمية

        # إنشاء قائمة تصفية مخصصة ومطورة
        self.create_custom_status_filter()

        # إضافة جميع العناصر للصف الواحد مع استغلال العرض الكامل داخل الإطار
        search_layout.addWidget(search_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.search_edit, 2, Qt.AlignVCenter)  # يأخذ مساحة أكبر
        search_layout.addWidget(filter_label, 0, Qt.AlignVCenter)
        search_layout.addWidget(self.status_filter_frame, 1, Qt.AlignVCenter)

        # تعيين التخطيط للإطار العلوي - استخدام الحاوي العمودي للتوسيط
        top_frame.setLayout(top_container)

        layout.addWidget(top_frame)

    def create_custom_status_filter(self):
        """إنشاء قائمة تصفية مخصصة ومطورة مطابقة للعملاء - نسخة محمية من الأخطاء"""
        try:
            # إنشاء إطار للقائمة المخصصة
            self.status_filter_frame = QFrame()

            # تطبيق التصميم بشكل آمن
            self._apply_filter_frame_style()

            # إنشاء تخطيط أفقي للإطار
            filter_layout = QHBoxLayout(self.status_filter_frame)
            filter_layout.setContentsMargins(5, 0, 5, 0)
            filter_layout.setSpacing(5)

            # إنشاء الأزرار والعناصر
            self._create_filter_arrows()
            self._create_filter_label()

            # إضافة العناصر للتخطيط
            filter_layout.addWidget(self.left_arrow, 0)
            filter_layout.addWidget(self.current_filter_label, 1)
            filter_layout.addWidget(self.filter_menu_button, 0)

            self.status_filter_frame.setLayout(filter_layout)

            # إنشاء القائمة المنسدلة
            self._create_filter_menu_safe()

            # ربط الأحداث
            self._connect_filter_events()

            # تعيين القيم الافتراضية
            self.current_filter_value = None
            self.status_filter = self.status_filter_frame

            print("✅ تم إنشاء قائمة التصفية بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قائمة تصفية الحالات: {str(e)}")
            self._create_fallback_filter()

    def _apply_filter_frame_style(self):
        """تطبيق تصميم الإطار بشكل آمن"""
        try:
            frame_style = """
                QFrame {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 15px;
                    padding: 6px 15px;
                    min-width: 520px;
                    max-width: 520px;
                    min-height: 33px;
                    max-height: 37px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    transition: all 0.3s ease;
                    cursor: pointer;
                }
                QFrame:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(250, 251, 255, 0.95),
                        stop:0.2 rgba(241, 245, 249, 0.9),
                        stop:0.4 rgba(226, 232, 240, 0.85),
                        stop:0.6 rgba(241, 245, 249, 0.9),
                        stop:0.8 rgba(250, 251, 255, 0.95),
                        stop:1 rgba(255, 255, 255, 0.9));
                    border: 4px solid rgba(96, 165, 250, 0.9);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
                    transform: translateY(-1px);
                }
            """
            self.status_filter_frame.setStyleSheet(frame_style)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الإطار: {e}")

    def _create_filter_arrows(self):
        """إنشاء أزرار الأسهم بشكل آمن"""
        try:
            arrow_style = """
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.8),
                        stop:0.2 rgba(59, 130, 246, 0.7),
                        stop:0.4 rgba(96, 165, 250, 0.6),
                        stop:0.6 rgba(139, 92, 246, 0.7),
                        stop:0.8 rgba(124, 58, 237, 0.8),
                        stop:1 rgba(109, 40, 217, 0.7));
                    border: 2px solid rgba(96, 165, 250, 0.6);
                    border-radius: 12px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: bold;
                    min-width: 35px;
                    max-width: 35px;
                    min-height: 25px;
                    max-height: 25px;
                    padding: 0px;
                    margin: 0px;
                    text-align: center;
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                    transition: all 0.2s ease;
                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
                }
                QPushButton:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(47, 109, 245, 0.9),
                        stop:0.2 rgba(69, 140, 255, 0.8),
                        stop:0.4 rgba(106, 175, 255, 0.7),
                        stop:0.6 rgba(149, 102, 255, 0.8),
                        stop:0.8 rgba(134, 68, 247, 0.9),
                        stop:1 rgba(119, 50, 227, 0.8));
                    border: 3px solid rgba(106, 175, 255, 0.8);
                    transform: translateY(-1px) scale(1.05);
                    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                }
                QPushButton:pressed {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(27, 89, 225, 1.0),
                        stop:0.2 rgba(49, 120, 235, 0.9),
                        stop:0.4 rgba(86, 155, 240, 0.8),
                        stop:0.6 rgba(129, 82, 236, 0.9),
                        stop:0.8 rgba(114, 48, 227, 1.0),
                        stop:1 rgba(99, 30, 207, 0.9));
                    border: 2px solid rgba(86, 155, 240, 0.9);
                    transform: translateY(0px) scale(0.98);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
                }
            """

            # زر السهم الأيسر
            self.left_arrow = QPushButton("▼")
            self.left_arrow.setStyleSheet(arrow_style)

            # زر القائمة (سهم يمين)
            self.filter_menu_button = QPushButton("▼")
            self.filter_menu_button.setStyleSheet(arrow_style)

        except Exception as e:
            print(f"خطأ في إنشاء الأسهم: {e}")
            # إنشاء أزرار بسيطة كبديل
            self.left_arrow = QPushButton("▼")
            self.filter_menu_button = QPushButton("▼")

    def _create_filter_label(self):
        """إنشاء تسمية التصفية بشكل آمن"""
        try:
            label_style = """
                QLabel {
                    background: transparent;
                    border: none;
                    font-size: 16px;
                    font-weight: 700;
                    color: #1f2937;
                    text-align: center;
                    padding: 0px;
                    margin: 0px;
                    min-width: 400px;
                    max-width: 400px;
                }
                QLabel:hover {
                    color: #1e40af;
                    font-weight: 800;
                    text-shadow: 0 1px 2px rgba(30, 64, 175, 0.3);
                }
            """

            self.current_filter_label = QLabel("جميع الحالات")
            self.current_filter_label.setStyleSheet(label_style)
            self.current_filter_label.setAlignment(Qt.AlignCenter)

        except Exception as e:
            print(f"خطأ في إنشاء التسمية: {e}")
            # إنشاء تسمية بسيطة كبديل
            self.current_filter_label = QLabel("جميع الحالات")
            self.current_filter_label.setAlignment(Qt.AlignCenter)

    def _create_filter_menu_safe(self):
        """إنشاء القائمة المنسدلة بشكل آمن"""
        try:
            self.filter_menu = QMenu(self)

            # تطبيق التصميم
            menu_style = """
                QMenu {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(255, 255, 255, 0.9),
                        stop:0.2 rgba(248, 250, 252, 0.95),
                        stop:0.4 rgba(241, 245, 249, 0.9),
                        stop:0.6 rgba(248, 250, 252, 0.95),
                        stop:0.8 rgba(255, 255, 255, 0.9),
                        stop:1 rgba(226, 232, 240, 0.85));
                    border: 3px solid rgba(96, 165, 250, 0.8);
                    border-radius: 4px;
                    padding: 8px;
                    color: #1f2937;
                    font-weight: 900;
                    font-size: 16px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                    min-width: 520px;
                    max-width: 520px;
                }
                QAction {
                    background: transparent;
                    color: #1f2937;
                    font-size: 16px;
                    font-weight: 700;
                    padding: 12px 20px;
                    margin: 2px;
                    border-radius: 8px;
                    text-align: center;
                    min-height: 25px;
                    border: 2px solid transparent;
                    transition: all 0.3s ease;
                }
                QAction:hover {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(59, 130, 246, 0.15),
                        stop:0.2 rgba(96, 165, 250, 0.2),
                        stop:0.4 rgba(139, 92, 246, 0.15),
                        stop:0.6 rgba(124, 58, 237, 0.2),
                        stop:0.8 rgba(109, 40, 217, 0.15),
                        stop:1 rgba(96, 165, 250, 0.1));
                    color: #1e40af;
                    border: 2px solid rgba(96, 165, 250, 0.4);
                    font-weight: 800;
                    transform: scale(1.02);
                    box-shadow: 0 2px 8px rgba(96, 165, 250, 0.3);
                }
                QAction:selected {
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 rgba(37, 99, 235, 0.2),
                        stop:0.2 rgba(59, 130, 246, 0.25),
                        stop:0.4 rgba(96, 165, 250, 0.2),
                        stop:0.6 rgba(139, 92, 246, 0.25),
                        stop:0.8 rgba(124, 58, 237, 0.2),
                        stop:1 rgba(109, 40, 217, 0.15));
                    color: #1e3a8a;
                    border: 2px solid rgba(59, 130, 246, 0.6);
                    font-weight: 900;
                    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
                }
            """

            self.filter_menu.setStyleSheet(menu_style)

            # إضافة العناصر
            self._add_filter_options()

        except Exception as e:
            print(f"خطأ في إنشاء القائمة: {e}")
            # إنشاء قائمة بسيطة كبديل
            self.filter_menu = QMenu(self)

    def _add_filter_options(self):
        """إضافة خيارات التصفية بشكل آمن"""
        try:
            filter_options = [
                ("جميع الحالات", None),
                ("⏰ أقساط معلقة", "pending"),
                ("✅ أقساط مسددة", "paid"),
                ("🚨 أقساط متأخرة", "overdue"),
                ("🚫 أقساط ملغية", "cancelled")
            ]

            for text, value in filter_options:
                try:
                    # إنشاء عنصر مع توسيط النص المثالي
                    centered_text = f"{text:^40}"
                    action = QAction(centered_text, self)
                    action.triggered.connect(lambda checked, v=value, t=text: self.set_filter(v, t))
                    self.filter_menu.addAction(action)
                except Exception as item_error:
                    print(f"خطأ في إضافة عنصر القائمة {text}: {item_error}")
                    continue

        except Exception as e:
            print(f"خطأ في إضافة خيارات التصفية: {e}")

    def _connect_filter_events(self):
        """ربط أحداث التصفية بشكل آمن"""
        try:
            # ربط الأزرار بالقائمة
            if hasattr(self, 'filter_menu_button'):
                self.filter_menu_button.clicked.connect(self.show_filter_menu)
            if hasattr(self, 'left_arrow'):
                self.left_arrow.clicked.connect(self.show_filter_menu)

            # إضافة ميزة الضغط على أي مكان في الإطار
            if hasattr(self, 'status_filter_frame'):
                self.status_filter_frame.mousePressEvent = self.frame_mouse_press_event
            if hasattr(self, 'current_filter_label'):
                self.current_filter_label.mousePressEvent = self.frame_mouse_press_event

            # جعل الإطار قابل للتركيز
            if hasattr(self, 'status_filter_frame'):
                self.status_filter_frame.setFocusPolicy(Qt.ClickFocus)

        except Exception as e:
            print(f"خطأ في ربط الأحداث: {e}")

    def _create_fallback_filter(self):
        """إنشاء تصفية بديلة بسيطة في حالة الفشل"""
        try:
            self.status_filter_frame = QLabel("جميع الحالات")
            self.status_filter_frame.setStyleSheet("""
                QLabel {
                    background: #f8f9fa;
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: bold;
                    color: #495057;
                    min-width: 200px;
                    text-align: center;
                }
            """)
            self.status_filter_frame.setAlignment(Qt.AlignCenter)
            self.current_filter_value = None
            self.status_filter = self.status_filter_frame
            print("✅ تم إنشاء تصفية بديلة بسيطة")
        except Exception as e:
            print(f"❌ خطأ حرج في إنشاء التصفية البديلة: {e}")
            # في أسوأ الحالات، إنشاء عنصر فارغ
            self.status_filter_frame = QLabel("تصفية")
            self.current_filter_value = None
            self.status_filter = self.status_filter_frame



    def frame_mouse_press_event(self, event):
        """التعامل مع الضغط على أي مكان في الإطار"""
        if event.button() == Qt.LeftButton:
            # إضافة تأثير بصري للضغط
            self.status_filter_frame.setFocus()
            self.show_filter_menu()

    def show_filter_menu(self):
        """عرض قائمة التصفية"""
        # تحديد موقع القائمة تحت الإطار مباشرة
        frame_pos = self.status_filter_frame.mapToGlobal(self.status_filter_frame.rect().bottomLeft())
        self.filter_menu.exec_(frame_pos)

    def set_filter(self, value, text):
        """تعيين قيمة التصفية"""
        self.current_filter_value = value
        self.current_filter_label.setText(text)
        self.filter_installments()

    def create_table_section(self, layout):
        """إنشاء قسم الجدول مطابق للفواتير"""
        # إنشاء جدول الأقساط المتطور والمحسن
        self.create_advanced_installments_table()

        layout.addWidget(self.installments_table, 1)  # إعطاء الجدول أولوية في التمدد

    def create_advanced_installments_table(self):
        """إنشاء جدول الأقساط المتطور والنظيف مطابق للفواتير"""
        # إنشاء الجدول
        self.installments_table = QTableWidget()
        self.installments_table.setColumnCount(8)

        # عناوين الأعمدة مع الأيقونات مطابقة للفواتير
        headers = [
            "🔢 ID",
            "🏦 رقم القسط",
            "🧑‍💼 العميل",
            "📆 تاريخ الإنشاء",
            "⏳ موعد الاستحقاق",
            "💎 إجمالي المبلغ",
            "💵 المبلغ المسدد",
            "🎯 حالة الدفع"
        ]
        self.installments_table.setHorizontalHeaderLabels(headers)

        # إعدادات الجدول مع التحديد المتعدد
        self.installments_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.installments_table.setSelectionMode(QTableWidget.ExtendedSelection)
        self.installments_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.installments_table.setAlternatingRowColors(False)
        self.installments_table.setSortingEnabled(True)

        # إعداد التحديد المتعدد
        self.init_multi_selection(self.installments_table)

        # إعدادات الصفوف والأعمدة
        self.installments_table.verticalHeader().setDefaultSectionSize(50)
        self.installments_table.verticalHeader().setVisible(False)

        header = self.installments_table.horizontalHeader()
        header.setFixedHeight(60)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setSectionResizeMode(QHeaderView.Stretch)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.installments_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.installments_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)  # ارتفاع الصف الواحد
                scrollbar.setPageStep(200)   # 4 صفوف للصفحة
        except Exception:
            pass

        # تطبيق التصميم والتفاعل
        self.apply_table_style()
        self.add_watermark_to_installments_table()
        self.setup_table_interactions()

    def apply_table_style(self):
        """تطبيق التصميم المتطور للجدول مطابق للفواتير"""
        self.installments_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }
            QTableWidget::item {
                padding: 10px 12px;
                border: 2px solid rgba(102, 126, 234, 0.12);
                border-left: 5px solid rgba(102, 126, 234, 0.5);
                border-right: 5px solid rgba(102, 126, 234, 0.5);
                border-top: 2px solid rgba(102, 126, 234, 0.2);
                border-bottom: 3px solid rgba(102, 126, 234, 0.3);
                text-align: center;
                min-height: 30px;
                max-height: 45px;
                font-weight: 600;
                font-size: 14px;
                border-radius: 15px;
                margin: 3px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(248, 250, 252, 1.0),
                    stop:0.3 rgba(241, 245, 249, 1.0),
                    stop:0.7 rgba(226, 232, 240, 1.0),
                    stop:1 rgba(203, 213, 225, 1.0));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 70, 229, 0.9), stop:0.2 rgba(99, 102, 241, 0.9),
                    stop:0.4 rgba(129, 140, 248, 0.9), stop:0.6 rgba(165, 180, 252, 0.9),
                    stop:0.8 rgba(196, 181, 253, 0.9), stop:1 rgba(221, 214, 254, 0.9)) !important;
                color: white !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #fbbf24 !important;
                border-right: 6px solid #fbbf24 !important;
                border-radius: 18px !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
            }
            QTableWidget::item:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 rgba(102, 126, 234, 0.15), stop:0.3 rgba(129, 140, 248, 0.2),
                    stop:0.7 rgba(165, 180, 252, 0.25), stop:1 rgba(196, 181, 253, 0.3)) !important;
                border: 3px solid rgba(102, 126, 234, 0.7) !important;
                border-left: 6px solid #06b6d4 !important;
                border-right: 6px solid #06b6d4 !important;
                border-radius: 16px !important;
                color: #0f172a !important;
                font-weight: bold !important;
                transform: translateY(-1px) !important;
            }
            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9), stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E40AF, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                height: 55px !important;
                letter-spacing: 1.3px !important;
                text-transform: uppercase !important;
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.2 #475569, stop:0.4 #2563EB,
                    stop:0.6 #6366F1, stop:0.8 #6D28D9, stop:1 #4C1D95) !important;
                transform: translateY(-2px) scale(1.02) !important;
                box-shadow: 0 6px 18px rgba(0, 0, 0, 0.5), 0 0 25px rgba(134, 158, 234, 0.4) !important;
                border: 4px solid rgba(255, 255, 255, 0.8) !important;
                text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.5px !important;
                font-weight: 900 !important;
            }
            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #334155, stop:0.4 #1E40AF,
                    stop:0.6 #2563EB, stop:0.8 #4C1D95, stop:1 #312E81) !important;
                transform: translateY(1px) scale(0.98) !important;
                box-shadow: inset 0 3px 6px rgba(0, 0, 0, 0.6) !important;
                border: 3px solid rgba(255, 255, 255, 0.9) !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                letter-spacing: 1.2px !important;
                font-weight: 900 !important;
            }
        """)

    def setup_table_interactions(self):
        """إعداد تفاعلات الجدول مطابق للفواتير"""
        try:
            # ربط إشارات الجدول
            self.installments_table.itemSelectionChanged.connect(self.on_selection_changed)
            self.installments_table.itemDoubleClicked.connect(self.view_installment)

            # إعداد قائمة النقر بالزر الأيمن
            self.installments_table.setContextMenuPolicy(Qt.CustomContextMenu)
            self.installments_table.customContextMenuRequested.connect(self.show_context_menu)

        except Exception as e:
            print(f"خطأ في إعداد تفاعلات الجدول: {e}")

    def show_context_menu(self, position):
        """عرض قائمة النقر بالزر الأيمن"""
        try:
            if self.installments_table.itemAt(position):
                menu = QMenu(self)

                view_action = QAction("👁️ عرض التفاصيل", self)
                view_action.triggered.connect(self.view_installment)
                menu.addAction(view_action)

                edit_action = QAction("✏️ تعديل", self)
                edit_action.triggered.connect(self.edit_installment)
                menu.addAction(edit_action)

                delete_action = QAction("🗑️ حذف", self)
                delete_action.triggered.connect(self.delete_installment)
                menu.addAction(delete_action)

                menu.exec_(self.installments_table.mapToGlobal(position))
        except Exception as e:
            print(f"خطأ في عرض قائمة السياق: {e}")

    def add_watermark_to_installments_table(self):
        """إضافة علامة مائية للجدول مطابقة للعملاء"""
        def paint_watermark(painter, rect):
            painter.save()
            painter.setOpacity(0.08)
            painter.setPen(QColor(30, 41, 59))
            font = QFont("Segoe UI", 180, QFont.Bold)
            painter.setFont(font)
            center_rect = rect.adjusted(0, rect.height() // 4, 0, -rect.height() // 4)
            painter.drawText(center_rect, Qt.AlignCenter, "Smart Finish")
            painter.restore()

        original_paint = self.installments_table.paintEvent
        def new_paint_event(event):
            try:
                original_paint(event)
                painter = QPainter(self.installments_table.viewport())
                paint_watermark(painter, self.installments_table.viewport().rect())
                painter.end()
            except Exception:
                pass

        self.installments_table.paintEvent = new_paint_event

    def create_buttons_section(self, layout):
        """إنشاء قسم الأزرار مطابق للفواتير"""
        # إنشاء إطار سفلي للأزرار متساوي مع الجدول وارتفاع أقل مطابق للفواتير
        bottom_frame = QFrame()
        bottom_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                border: 3px solid #000000;
                border-radius: 10px;
                margin: 1px;
                padding: 0px;
                max-height: 75px;
                min-height: 70px;
            }
        """)
        actions_layout = QHBoxLayout()
        actions_layout.setContentsMargins(0, 0, 0, 0)  # إزالة الهوامش للتوسيط الدقيق
        actions_layout.setSpacing(4)  # مسافة أكبر بين الأزرار لتوزيع أفضل

        # إنشاء حاوي عمودي للتوسيط الحقيقي
        bottom_container = QVBoxLayout()
        bottom_container.setContentsMargins(6, 0, 6, 0)  # هوامش جانبية فقط
        bottom_container.setSpacing(0)

        # إضافة مساحة فارغة أعلى للتوسيط
        bottom_container.addStretch(1)

        # إضافة التخطيط الأفقي في المنتصف
        bottom_container.addLayout(actions_layout)

        # إضافة مساحة فارغة أسفل للتوسيط
        bottom_container.addStretch(1)

        # إنشاء الأزرار مطابقة للفواتير

        # زر الإضافة
        self.add_button = QPushButton("➕ إضافة قسط")
        self.style_advanced_button(self.add_button, 'emerald')
        self.add_button.clicked.connect(self.add_installment)

        # زر التعديل
        self.edit_button = QPushButton("✏️ تعديل")
        self.style_advanced_button(self.edit_button, 'info')
        self.edit_button.clicked.connect(self.edit_installment)

        # زر الحذف
        self.delete_button = QPushButton("🗑️ حذف")
        self.style_advanced_button(self.delete_button, 'danger')
        self.delete_button.clicked.connect(self.delete_installment)

        # زر التحديث
        self.refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.refresh_button, 'secondary')
        self.refresh_button.clicked.connect(self.refresh_data)

        # زر عرض التفاصيل
        self.view_button = QPushButton("👁️ عرض التفاصيل")
        self.style_advanced_button(self.view_button, 'indigo')
        self.view_button.clicked.connect(self.view_installment)

        # زر التصدير
        self.export_button = QPushButton("📤 تصدير")
        self.style_advanced_button(self.export_button, 'info')

        # إنشاء قائمة التصدير
        self.create_export_menu()

        # زر إدارة الوثائق
        self.documents_button = QPushButton("📁 إدارة الوثائق")
        self.style_advanced_button(self.documents_button, 'purple')
        self.documents_button.clicked.connect(self.manage_documents)

        # زر الاتصالات (واتساب)
        self.whatsapp_button = QPushButton("📞 اتصال واتساب")
        self.style_advanced_button(self.whatsapp_button, 'emerald')
        self.whatsapp_button.clicked.connect(self.show_whatsapp_options)

        # زر الإحصائيات
        self.statistics_button = QPushButton("📊 إحصائيات")
        self.style_advanced_button(self.statistics_button, 'indigo')
        self.statistics_button.clicked.connect(self.show_statistics)

        # زر إظهار/إخفاء الأعمدة
        self.columns_visibility_button = QPushButton("👁️ الأعمدة")
        self.style_advanced_button(self.columns_visibility_button, 'teal')
        self.columns_visibility_button.clicked.connect(self.toggle_columns_visibility)



        # إضافة جميع الأزرار للتخطيط
        actions_layout.addWidget(self.add_button)
        actions_layout.addWidget(self.edit_button)
        actions_layout.addWidget(self.delete_button)
        actions_layout.addWidget(self.refresh_button)
        actions_layout.addWidget(self.view_button)
        actions_layout.addWidget(self.documents_button)
        actions_layout.addWidget(self.export_button)
        actions_layout.addWidget(self.whatsapp_button)
        actions_layout.addWidget(self.statistics_button)
        actions_layout.addWidget(self.columns_visibility_button)

        # تعيين التخطيط للإطار السفلي
        bottom_frame.setLayout(bottom_container)

        # تجميع التخطيط النهائي
        layout.addWidget(bottom_frame)

    def create_export_menu(self):
        """إنشاء قائمة التصدير مطابقة للفواتير"""
        try:
            self.export_menu = QMenu(self)

            # خيارات التصدير
            export_options = [
                ("📄 تصدير إلى PDF", self.export_pdf),
                ("📊 تصدير إلى Excel", self.export_excel),
                ("📋 تصدير إلى CSV", self.export_csv),
                ("🖨️ طباعة", self.print_installments),
            ]

            for text, function in export_options:
                action = QAction(text, self)
                action.triggered.connect(function)
                self.export_menu.addAction(action)

            if self.export_button:
                self.export_button.setMenu(self.export_menu)
        except Exception as e:
            print(f"خطأ في إنشاء قائمة التصدير: {e}")

    def show_statistics(self):
        """عرض إحصائيات الأقساط"""
        show_info_message("قريباً", "ميزة الإحصائيات ستكون متاحة قريباً")

    def toggle_columns_visibility(self):
        """تبديل إظهار/إخفاء الأعمدة"""
        show_info_message("قريباً", "ميزة إظهار/إخفاء الأعمدة ستكون متاحة قريباً")

    def load_data(self):
        """تحميل بيانات الأقساط مطابق للفواتير"""
        try:
            # استعلام الأقساط من قاعدة البيانات
            installments = self.session.query(Installment).order_by(Installment.id.desc()).all()

            # تحديث الجدول
            self.installments_table.setRowCount(len(installments))

            for row, installment in enumerate(installments):
                # الرقم مع أيقونة
                balance = (installment.total_amount or 0) - (installment.paid_amount or 0)
                if balance > 0:
                    icon = "🔴"  # أحمر للمتبقي
                elif balance == 0:
                    icon = "💰"  # ذهبي للمكتمل
                else:
                    icon = "🔢"  # رقم عادي

                id_item = QTableWidgetItem(f"{icon} #{installment.id}")
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setForeground(QColor("#000000"))
                self.installments_table.setItem(row, 0, id_item)

                # رقم القسط
                number_item = QTableWidgetItem(installment.installment_number or "غير محدد")
                number_item.setTextAlignment(Qt.AlignCenter)
                self.installments_table.setItem(row, 1, number_item)

                # العميل
                client_name = installment.client.name if installment.client else "غير محدد"
                client_item = QTableWidgetItem(client_name)
                client_item.setTextAlignment(Qt.AlignCenter)
                self.installments_table.setItem(row, 2, client_item)

                # التاريخ
                date_str = installment.date.strftime('%Y-%m-%d') if installment.date else "غير محدد"
                date_item = QTableWidgetItem(date_str)
                date_item.setTextAlignment(Qt.AlignCenter)
                self.installments_table.setItem(row, 3, date_item)

                # تاريخ الاستحقاق
                due_date = installment.due_date.strftime('%Y-%m-%d') if installment.due_date else "غير محدد"
                due_item = QTableWidgetItem(due_date)
                due_item.setTextAlignment(Qt.AlignCenter)
                self.installments_table.setItem(row, 4, due_item)

                # المبلغ الإجمالي
                total_amount = installment.total_amount or 0
                if total_amount == 0:
                    total_text = "لا توجد بيانات"
                    total_color = QColor("#ef4444")  # أحمر
                else:
                    total_text = f"{total_amount:.0f} جنيه"
                    total_color = QColor("#000000")  # أسود

                total_item = QTableWidgetItem(total_text)
                total_item.setTextAlignment(Qt.AlignCenter)
                total_item.setForeground(total_color)
                self.installments_table.setItem(row, 5, total_item)

                # المبلغ المدفوع
                paid_amount = installment.paid_amount or 0
                if paid_amount == 0:
                    paid_text = "لا توجد بيانات"
                    paid_color = QColor("#ef4444")  # أحمر
                else:
                    paid_text = f"{paid_amount:.0f} جنيه"
                    paid_color = QColor("#000000")  # أسود

                paid_item = QTableWidgetItem(paid_text)
                paid_item.setTextAlignment(Qt.AlignCenter)
                paid_item.setForeground(paid_color)
                self.installments_table.setItem(row, 6, paid_item)

                # الحالة مع تلوين
                status_text = self.get_status_text(installment.status)
                status_item = QTableWidgetItem(status_text)
                status_item.setTextAlignment(Qt.AlignCenter)

                # تلوين الحالة
                status_colors = {
                    'معلق': QColor("#f59e0b"),      # أصفر
                    'مدفوع': QColor("#10b981"),     # أخضر
                    'متأخر': QColor("#ef4444"),     # أحمر
                    'ملغي': QColor("#6b7280")       # رمادي
                }
                if status_text in status_colors:
                    status_item.setForeground(status_colors[status_text])
                else:
                    status_item.setForeground(QColor("#000000"))

                self.installments_table.setItem(row, 7, status_item)



            # تحديث حالة الأزرار
            self.on_selection_changed()

            print(f"✅ تم تحميل {len(installments)} قسط بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تحميل بيانات الأقساط: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ في تحميل البيانات: {str(e)}")

    def get_status_text(self, status):
        """تحويل حالة القسط إلى نص عربي"""
        status_map = {
            'pending': 'معلق',
            'paid': 'مدفوع',
            'overdue': 'متأخر',
            'cancelled': 'ملغي'
        }
        return status_map.get(status, 'غير محدد')

    def filter_installments(self):
        """تصفية الأقساط مطابق للفواتير مع النظام المطور"""
        try:
            search_text = self.search_edit.text().lower().strip()

            # استخدام النظام الجديد للتصفية
            status_value = getattr(self, 'current_filter_value', None)

            for row in range(self.installments_table.rowCount()):
                show_row = True

                # تصفية النص
                if search_text:
                    row_text = ""
                    for col in range(self.installments_table.columnCount()):
                        item = self.installments_table.item(row, col)
                        if item:
                            row_text += item.text().lower() + " "

                    if search_text not in row_text:
                        show_row = False

                # تصفية الحالة باستخدام النظام الجديد
                if show_row and status_value is not None:
                    status_item = self.installments_table.item(row, 7)
                    if status_item:
                        status_text = status_item.text()
                        # تحويل النص العربي إلى القيمة الإنجليزية للمقارنة
                        status_map = {
                            'معلق': 'pending',
                            'مدفوع': 'paid',
                            'متأخر': 'overdue',
                            'ملغي': 'cancelled'
                        }

                        # البحث عن القيمة المقابلة للنص العربي
                        current_status = None
                        for arabic_text, english_value in status_map.items():
                            if arabic_text in status_text:
                                current_status = english_value
                                break

                        # إخفاء الصف إذا لم يطابق الفلتر
                        if current_status != status_value:
                            show_row = False

                self.installments_table.setRowHidden(row, not show_row)

        except Exception as e:
            print(f"❌ خطأ في تصفية الأقساط: {str(e)}")

    def on_selection_changed(self):
        """تحديث حالة الأزرار عند تغيير التحديد"""
        try:
            if not self.installments_table:
                return

            has_selection = len(self.installments_table.selectedItems()) > 0

            if hasattr(self, 'edit_button') and self.edit_button:
                self.edit_button.setEnabled(has_selection)
            if hasattr(self, 'delete_button') and self.delete_button:
                self.delete_button.setEnabled(has_selection)
            if hasattr(self, 'view_button') and self.view_button:
                self.view_button.setEnabled(has_selection)
            if hasattr(self, 'documents_button') and self.documents_button:
                self.documents_button.setEnabled(has_selection)
            if hasattr(self, 'whatsapp_button') and self.whatsapp_button:
                self.whatsapp_button.setEnabled(has_selection)
        except Exception as e:
            print(f"خطأ في تحديث حالة الأزرار: {e}")

    def refresh_data(self):
        """تحديث البيانات مطابق للعملاء"""
        try:
            self.load_data()
            show_info_message("تم", "تم تحديث بيانات الأقساط بنجاح")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تحديث البيانات: {str(e)}")

    def add_installment(self):
        """إضافة قسط جديد"""
        try:
            dialog = AddInstallmentDialog(self.session, self)
            if dialog.exec_() == QDialog.Accepted:
                self.load_data()  # تحديث الجدول بعد الإضافة
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في إضافة القسط: {str(e)}")

    def edit_installment(self):
        """تعديل قسط"""
        try:
            current_row = self.installments_table.currentRow()
            if current_row >= 0:
                # الحصول على ID القسط من العمود الأول مع إزالة الأيقونات
                id_item = self.installments_table.item(current_row, 0)
                if id_item:
                    # استخراج الأرقام فقط من النص (إزالة الأيقونات والرموز)
                    id_text = id_item.text()
                    import re
                    numbers = re.findall(r'\d+', id_text)
                    if numbers:
                        installment_id = int(numbers[0])
                    else:
                        show_error_message("خطأ", "لا يمكن استخراج معرف القسط")
                        return
                    # الحصول على بيانات القسط من قاعدة البيانات
                    installment = self.session.query(Installment).get(installment_id)
                    if installment:
                        # فتح نافذة التعديل
                        dialog = AddInstallmentDialog(self.session, self, installment)
                        if dialog.exec_() == QDialog.Accepted:
                            self.load_data()  # تحديث الجدول بعد التعديل
                    else:
                        show_error_message("خطأ", "لم يتم العثور على القسط المحدد")
                else:
                    show_error_message("خطأ", "لم يتم تحديد قسط صحيح")
            else:
                show_error_message("تحذير", "يرجى اختيار قسط للتعديل")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في تعديل القسط: {str(e)}")

    def delete_installment(self):
        """حذف قسط"""
        show_info_message("قريباً", "ميزة حذف القسط قيد التطوير")

    def view_installment(self):
        """عرض تفاصيل القسط مطابق للعملاء"""
        try:
            current_row = self.installments_table.currentRow()
            if current_row >= 0:
                # الحصول على ID القسط من العمود الأول مع إزالة الأيقونات
                id_item = self.installments_table.item(current_row, 0)
                if id_item:
                    # استخراج الأرقام فقط من النص (إزالة الأيقونات والرموز)
                    id_text = id_item.text()
                    import re
                    numbers = re.findall(r'\d+', id_text)
                    if numbers:
                        installment_id = int(numbers[0])
                        # الحصول على بيانات القسط من قاعدة البيانات
                        installment = self.session.query(Installment).get(installment_id)
                        if installment:
                            # فتح نافذة عرض التفاصيل
                            dialog = InstallmentInfoDialog(self, installment)
                            dialog.exec_()
                        else:
                            show_error_message("خطأ", "لم يتم العثور على القسط المحدد")
                    else:
                        show_error_message("خطأ", "لا يمكن استخراج معرف القسط")
                else:
                    show_error_message("خطأ", "لم يتم تحديد قسط صحيح")
            else:
                show_error_message("تحذير", "يرجى اختيار قسط لعرض التفاصيل")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ في عرض تفاصيل القسط: {str(e)}")

    def manage_documents(self):
        """إدارة وثائق العميل المرتبط بالقسط"""
        try:
            # التحقق من وجود تحديد
            selected_items = self.installments_table.selectedItems()
            if not selected_items:
                show_info_message("تنبيه", "الرجاء اختيار قسط من الجدول أولاً")
                return

            # الحصول على بيانات القسط المحدد
            row = selected_items[0].row()
            installment_id_text = self.installments_table.item(row, 0).text()

            # استخراج الأرقام فقط من النص
            import re
            numbers = re.findall(r'\d+', installment_id_text)
            if not numbers:
                show_error_message("خطأ", "لا يمكن الحصول على معرف القسط")
                return

            installment_id = int(numbers[0])
            installment = self.session.query(Installment).get(installment_id)

            if not installment or not installment.client:
                show_error_message("خطأ", "لم يتم العثور على العميل المرتبط بهذا القسط")
                return

            print(f"✅ تم العثور على العميل: {installment.client.name}")

            # فتح نافذة إدارة الوثائق (سيتم استيرادها من قسم العملاء)
            from ui.clients import ClientDocumentsDialog
            dialog = ClientDocumentsDialog(self, client=installment.client, session=self.session)
            dialog.exec_()

        except Exception as e:
            print(f"❌ خطأ في إدارة وثائق العميل: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ في إدارة وثائق العميل: {str(e)}")

    def show_whatsapp_options(self):
        """عرض خيارات الواتساب للعميل المرتبط بالقسط"""
        try:
            # التحقق من وجود تحديد
            selected_items = self.installments_table.selectedItems()
            if not selected_items:
                show_info_message("تنبيه", "الرجاء اختيار قسط من الجدول أولاً")
                return

            # الحصول على بيانات القسط المحدد
            row = selected_items[0].row()
            installment_id_text = self.installments_table.item(row, 0).text()

            # استخراج الأرقام فقط من النص
            import re
            numbers = re.findall(r'\d+', installment_id_text)
            if not numbers:
                show_error_message("خطأ", "لا يمكن الحصول على معرف القسط")
                return

            installment_id = int(numbers[0])
            installment = self.session.query(Installment).get(installment_id)

            if not installment or not installment.client:
                show_error_message("خطأ", "لم يتم العثور على العميل المرتبط بهذا القسط")
                return

            # عرض نافذة خيارات الواتساب (سيتم استيرادها من قسم العملاء)
            from ui.clients import WhatsAppDialog
            dialog = WhatsAppDialog(installment.client, self)
            dialog.exec_()

        except Exception as e:
            print(f"❌ خطأ في عرض خيارات الواتساب: {str(e)}")
            show_error_message("خطأ", f"حدث خطأ في عرض خيارات الواتساب: {str(e)}")

    def export_pdf(self):
        """تصدير إلى PDF"""
        show_info_message("قريباً", "ميزة التصدير إلى PDF ستكون متاحة قريباً")

    def export_excel(self):
        """تصدير إلى Excel"""
        show_info_message("قريباً", "ميزة التصدير إلى Excel ستكون متاحة قريباً")

    def export_csv(self):
        """تصدير إلى CSV"""
        show_info_message("قريباً", "ميزة التصدير إلى CSV ستكون متاحة قريباً")

    def print_installments(self):
        """طباعة الأقساط"""
        show_info_message("قريباً", "ميزة الطباعة ستكون متاحة قريباً")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر - مطابق للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'secondary': {
                    'bg_start': '#374151', 'bg_mid': '#4b5563', 'bg_end': '#6b7280', 'bg_bottom': '#9ca3af',
                    'hover_start': '#6b7280', 'hover_mid': '#9ca3af', 'hover_end': '#d1d5db', 'hover_bottom': '#f3f4f6',
                    'hover_border': '#9ca3af', 'pressed_start': '#1f2937', 'pressed_mid': '#374151',
                    'pressed_end': '#4b5563', 'pressed_bottom': '#6b7280', 'pressed_border': '#4b5563',
                    'border': '#9ca3af', 'text': '#ffffff', 'shadow': 'rgba(156, 163, 175, 0.6)'
                },
                'indigo': {
                    'bg_start': '#312e81', 'bg_mid': '#3730a3', 'bg_end': '#4338ca', 'bg_bottom': '#4f46e5',
                    'hover_start': '#4f46e5', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'purple': {
                    'bg_start': '#581c87', 'bg_mid': '#6b21a8', 'bg_end': '#7e22ce', 'bg_bottom': '#9333ea',
                    'hover_start': '#9333ea', 'hover_mid': '#a855f7', 'hover_end': '#c084fc', 'hover_bottom': '#ddd6fe',
                    'hover_border': '#a855f7', 'pressed_start': '#3b0764', 'pressed_mid': '#581c87',
                    'pressed_end': '#6b21a8', 'pressed_bottom': '#7e22ce', 'pressed_border': '#6b21a8',
                    'border': '#a855f7', 'text': '#ffffff', 'shadow': 'rgba(168, 85, 247, 0.6)'
                },
                'teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#042f2e', 'pressed_mid': '#134e4a',
                    'pressed_end': '#0f766e', 'pressed_bottom': '#0d9488', 'pressed_border': '#0f766e',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                }
            }

            # تحديد اللون حسب نوع الزر
            color = colors.get(button_type, colors['secondary'])

            # إنشاء التصميم المتطور مع التدرجات والتأثيرات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color['bg_start']},
                        stop:0.3 {color['bg_mid']},
                        stop:0.7 {color['bg_end']},
                        stop:1 {color['bg_bottom']});
                    color: {color['text']};
                    border: 4px solid {color['border']};
                    border-radius: 20px;
                    padding: 18px 24px;
                    font-size: 14px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               2px 2px 4px rgba(0, 0, 0, 0.7),
                               1px 1px 2px rgba(0, 0, 0, 0.5);
                    box-shadow: 0 8px 20px {color['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 25px {color['shadow']},
                               0 0 40px rgba(255, 255, 255, 0.1);
                    letter-spacing: 0.5px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color['hover_start']},
                        stop:0.3 {color['hover_mid']},
                        stop:0.7 {color['hover_end']},
                        stop:1 {color['hover_bottom']});
                    border: 5px solid {color['hover_border']};
                    transform: translateY(-3px) scale(1.05);
                    box-shadow: 0 12px 30px {color['shadow']},
                               inset 0 4px 0 rgba(255, 255, 255, 0.6),
                               inset 0 -4px 0 rgba(0, 0, 0, 0.6),
                               0 0 35px {color['shadow']},
                               0 0 50px rgba(255, 255, 255, 0.2);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {color['pressed_start']},
                        stop:0.3 {color['pressed_mid']},
                        stop:0.7 {color['pressed_end']},
                        stop:1 {color['pressed_bottom']});
                    border: 3px solid {color['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: 0 4px 10px {color['shadow']},
                               inset 0 2px 0 rgba(0, 0, 0, 0.4),
                               inset 0 -2px 0 rgba(255, 255, 255, 0.2),
                               0 0 15px {color['shadow']};
                }}
            """

            button.setStyleSheet(style)
        except Exception as e:
            print(f"خطأ في تطبيق تصميم الزر: {e}")
            # تطبيق تصميم افتراضي في حالة الخطأ
            button.setStyleSheet("""
                QPushButton {
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #374151, stop:0.3 #4b5563, stop:0.7 #6b7280, stop:1 #9ca3af);
                    color: #ffffff;
                    border: 4px solid #9ca3af;
                    border-radius: 20px;
                    padding: 18px 24px;
                    font-size: 14px;
                    font-weight: bold;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                }
            """)


class AddInstallmentDialog(QDialog):
    """نافذة إضافة أو تعديل قسط مطابقة تماماً لنافذة العملاء"""

    def __init__(self, session, parent=None, installment=None):
        super().__init__(parent)
        self.session = session
        self.parent_widget = parent  # حفظ مرجع للوالد
        self.installment = installment  # القسط للتعديل (None للإضافة)
        self.is_edit_mode = installment is not None  # تحديد وضع التعديل

        self.setModal(True)
        self.resize(650, 850)  # نفس حجم نافذة العملاء تماماً
        self.setup_ui()

        # تحميل البيانات في وضع التعديل
        if self.is_edit_mode:
            self.load_installment_data()

    def setup_ui(self):
        """إعداد واجهة النافذة مطابقة لنافذة العملاء"""
        # عنوان النافذة
        if self.is_edit_mode:
            self.setWindowTitle("✏️ تعديل قسط - نظام إدارة الأقساط المتطور والشامل")
        else:
            self.setWindowTitle("➕ إضافة قسط جديد - نظام إدارة الأقساط المتطور والشامل")

        # إزالة علامة الاستفهام وتحسين شريط العنوان
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)

        # تخصيص شريط العنوان مطابق لنافذة العملاء
        self.customize_title_bar()

        # تطبيق نمط النافذة مطابق للعملاء
        self.setStyleSheet(self.get_dialog_styling())

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إنشاء النموذج
        self.create_form()

        # إنشاء الأزرار
        self.create_buttons()

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق للعملاء"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"خطأ في تخصيص شريط العنوان: {e}")

    def get_dialog_styling(self):
        """الحصول على تصميم النافذة مطابق تماماً للعملاء"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }

            QLabel {
                color: white;
                font-size: 14px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
                margin: 5px 0;
            }

            QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox, QDateEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(139, 92, 246, 0.7);
                border-radius: 10px;
                padding: 12px 15px;
                font-size: 13px;
                font-weight: bold;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                selection-color: #1f2937;
            }

            QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus, QDateEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
                box-shadow: 0 6px 16px rgba(96, 165, 250, 0.4);
                transform: scale(1.02);
            }
        """

    def create_form(self):
        """إنشاء النموذج مطابق تماماً لنافذة العملاء"""
        # إنشاء دالة لتصميم النصوص مع عرض مقلل مطابقة للعملاء
        def create_styled_label(text, icon, required=False):
            label = QLabel(f"{icon} {text}")
            if required:
                label.setText(f"{icon} {text} *")
            label.setStyleSheet("""
                QLabel {
                    color: #ffffff;
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 rgba(59, 130, 246, 0.8),
                        stop:0.3 rgba(96, 165, 250, 0.7),
                        stop:0.7 rgba(139, 92, 246, 0.7),
                        stop:1 rgba(124, 58, 237, 0.8));
                    border: 2px solid rgba(96, 165, 250, 0.9);
                    border-radius: 5px;
                    padding: 8px 12px;
                    font-weight: bold;
                    font-size: 16px;
                    min-width: 120px;
                    max-width: 120px;
                    text-align: center;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
                    box-shadow: 0 2px 6px rgba(96, 165, 250, 0.4);
                }
            """)
            label.setAlignment(Qt.AlignCenter)
            return label

        # إنشاء النموذج
        form_layout = QFormLayout()
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight)

        # رقم القسط (تلقائي في حالة الإضافة)
        self.installment_number_edit = QLineEdit()
        self.installment_number_edit.setPlaceholderText("سيتم إنشاؤه تلقائياً...")
        if not self.is_edit_mode:
            self.installment_number_edit.setReadOnly(True)
        form_layout.addRow(create_styled_label("رقم القسط", "🏦"), self.installment_number_edit)

        # العميل
        self.client_combo = QComboBox()
        self.client_combo.setEditable(True)
        self.client_combo.setInsertPolicy(QComboBox.NoInsert)
        self.load_clients()
        form_layout.addRow(create_styled_label("العميل", "🧑‍💼", True), self.client_combo)

        # تاريخ الإنشاء
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        form_layout.addRow(create_styled_label("تاريخ الإنشاء", "📆"), self.date_edit)

        # موعد الاستحقاق
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))  # افتراضي 30 يوم
        self.due_date_edit.setCalendarPopup(True)
        form_layout.addRow(create_styled_label("موعد الاستحقاق", "⏳", True), self.due_date_edit)

        # إجمالي المبلغ
        self.total_amount_spinbox = QDoubleSpinBox()
        self.total_amount_spinbox.setRange(0, 999999999)
        self.total_amount_spinbox.setDecimals(2)
        self.total_amount_spinbox.setSuffix(" ج.م")
        form_layout.addRow(create_styled_label("إجمالي المبلغ", "💎", True), self.total_amount_spinbox)

        # المبلغ المسدد
        self.paid_amount_spinbox = QDoubleSpinBox()
        self.paid_amount_spinbox.setRange(0, 999999999)
        self.paid_amount_spinbox.setDecimals(2)
        self.paid_amount_spinbox.setSuffix(" ج.م")
        form_layout.addRow(create_styled_label("المبلغ المسدد", "💵"), self.paid_amount_spinbox)

        # حالة الدفع
        self.status_combo = QComboBox()
        self.status_combo.addItems([
            "pending - معلق",
            "paid - مسدد",
            "overdue - متأخر",
            "cancelled - ملغي"
        ])
        form_layout.addRow(create_styled_label("حالة الدفع", "🎯"), self.status_combo)

        # الملاحظات
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل أي ملاحظات إضافية...")
        self.notes_edit.setMaximumHeight(100)
        form_layout.addRow(create_styled_label("الملاحظات", "📝"), self.notes_edit)

        # إضافة النموذج للتخطيط الرئيسي
        self.layout().addLayout(form_layout)

    def load_clients(self):
        """تحميل قائمة العملاء"""
        try:
            clients = self.session.query(Client).order_by(Client.name).all()
            self.client_combo.clear()
            self.client_combo.addItem("اختر العميل...", None)

            for client in clients:
                display_text = f"{client.name}"
                if client.phone:
                    display_text += f" - {client.phone}"
                self.client_combo.addItem(display_text, client.id)

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل العملاء: {str(e)}")

    def create_buttons(self):
        """إنشاء الأزرار مطابقة تماماً لنافذة العملاء"""
        # إطار الأزرار مطابق للعملاء
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(226, 232, 240, 0.1),
                    stop:1 rgba(203, 213, 224, 0.08));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                margin: 15px 5px 5px 5px;
                padding: 20px;
                min-height: 80px;
                max-height: 90px;
            }
            QFrame:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.2),
                    stop:0.3 rgba(248, 250, 252, 0.17),
                    stop:0.7 rgba(226, 232, 240, 0.15),
                    stop:1 rgba(203, 213, 224, 0.12));
                border: 3px solid rgba(255, 255, 255, 0.4);
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(20)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumHeight(50)
        save_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(save_btn, 'emerald')
        save_btn.clicked.connect(self.save_installment)

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumHeight(50)
        cancel_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(cancel_btn, 'danger')
        cancel_btn.clicked.connect(self.reject)

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        self.layout().addWidget(buttons_frame)

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار مطابق تماماً للعملاء"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم بسيط كبديل مطابق للعملاء
                colors = {
                    'emerald': '#10b981',
                    'danger': '#ef4444',
                    'info': '#3b82f6',
                    'primary': '#4f46e5'
                }
                color = colors.get(button_type, '#6b7280')
                button.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {color};
                        color: white;
                        border: none;
                        border-radius: 8px;
                        padding: 10px 20px;
                        font-weight: bold;
                    }}
                    QPushButton:hover {{
                        background-color: {color}dd;
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def load_installment_data(self):
        """تحميل بيانات القسط في وضع التعديل"""
        if not self.installment:
            return

        try:
            # رقم القسط
            self.installment_number_edit.setText(self.installment.installment_number or "")

            # العميل
            if self.installment.client_id:
                for i in range(self.client_combo.count()):
                    if self.client_combo.itemData(i) == self.installment.client_id:
                        self.client_combo.setCurrentIndex(i)
                        break

            # التواريخ
            if self.installment.date:
                self.date_edit.setDate(QDate.fromString(self.installment.date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

            if self.installment.due_date:
                self.due_date_edit.setDate(QDate.fromString(self.installment.due_date.strftime("%Y-%m-%d"), "yyyy-MM-dd"))

            # المبالغ
            self.total_amount_spinbox.setValue(self.installment.total_amount or 0)
            self.paid_amount_spinbox.setValue(self.installment.paid_amount or 0)

            # الحالة
            status_map = {
                'pending': 0,
                'paid': 1,
                'overdue': 2,
                'cancelled': 3
            }
            status_index = status_map.get(self.installment.status, 0)
            self.status_combo.setCurrentIndex(status_index)

            # الملاحظات
            self.notes_edit.setPlainText(self.installment.notes or "")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في تحميل بيانات القسط: {str(e)}")

    def save_installment(self):
        """حفظ القسط الجديد أو تحديث القسط الموجود"""
        try:
            # التحقق من صحة البيانات مطابق للعملاء
            client_id = self.client_combo.currentData()
            if not client_id:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "تحذير", "يرجى اختيار العميل")
                self.client_combo.setFocus()
                return

            total_amount = self.total_amount_spinbox.value()
            if total_amount <= 0:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "تحذير", "يرجى إدخال مبلغ صحيح")
                self.total_amount_spinbox.setFocus()
                return

            # استخراج الحالة
            status_text = self.status_combo.currentText()
            status = status_text.split(' - ')[0]

            if self.is_edit_mode:
                # تحديث القسط الموجود
                self.installment.client_id = client_id
                self.installment.date = self.date_edit.date().toPyDate()
                self.installment.due_date = self.due_date_edit.date().toPyDate()
                self.installment.total_amount = total_amount
                self.installment.paid_amount = self.paid_amount_spinbox.value()
                self.installment.status = status
                self.installment.notes = self.notes_edit.toPlainText().strip() or None

                self.session.commit()

                # رسالة نجاح التحديث مطابقة للعملاء
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "نجح",
                    f"تم تحديث القسط '{self.installment.installment_number}' بنجاح!"
                )

            else:
                # إنشاء قسط جديد
                installment_number = generate_installment_number(self.session)

                new_installment = Installment(
                    installment_number=installment_number,
                    client_id=client_id,
                    date=self.date_edit.date().toPyDate(),
                    due_date=self.due_date_edit.date().toPyDate(),
                    total_amount=total_amount,
                    paid_amount=self.paid_amount_spinbox.value(),
                    status=status,
                    notes=self.notes_edit.toPlainText().strip() or None
                )

                self.session.add(new_installment)
                self.session.commit()

                # رسالة نجاح الإضافة مطابقة للعملاء
                client = self.session.query(Client).get(client_id)
                client_name = client.name if client else "غير محدد"
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(
                    self,
                    "نجح",
                    f"تم إضافة القسط '{installment_number}' للعميل '{client_name}' بنجاح!\nالمبلغ الإجمالي: {total_amount:.2f} ج.م"
                )

            self.accept()

        except Exception as e:
            self.session.rollback()
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ القسط: {str(e)}")


class InstallmentInfoDialog(QDialog):
    """
    نافذة عرض معلومات القسط التفصيلية - مطابقة تماماً لنافذة العملاء

    هذه النافذة تعتبر مطابقة للنموذج المرجعي لجميع نوافذ المعلومات في النظام
    المميزات:
    - تصميم موحد ومتسق مع حواف مربعة
    - ألوان واضحة ومتباينة للبيانات
    - تخطيط منظم ومرن
    - أزرار وظيفية متطورة
    - أيقونات محسنة ومتطورة
    """

    def __init__(self, parent=None, installment=None):
        super().__init__(parent)
        self.installment = installment
        self.parent_widget = parent
        self.setup_ui()

    def setup_ui(self):
        """إعداد واجهة نافذة المعلومات المرجعية"""
        # ═══════════════════════════════════════════════════════════════
        # إعدادات النافذة الأساسية
        # ═══════════════════════════════════════════════════════════════
        self.setWindowTitle("💳📋 معلومات القسط - نظام إدارة الأقساط المتطور والشامل")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setWindowIcon(self.create_window_icon())
        self.customize_title_bar()
        self.setModal(True)
        self.resize(850, 780)  # حجم محسن للعرض الأمثل

        # ═══════════════════════════════════════════════════════════════
        # تصميم النافذة والخلفية المرجعية
        # ═══════════════════════════════════════════════════════════════
        self.setStyleSheet(self.get_reference_styling() + """
            QWidget {
                background: transparent;
            }
            QVBoxLayout, QHBoxLayout {
                background: transparent;
            }
        """)

        # التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)
        layout.setSpacing(20)

        # إنشاء منطقة التمرير مطابقة للعملاء
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background: transparent;
            }
            QScrollArea > QWidget > QWidget {
                background: transparent;
            }
            QScrollBar:vertical {
                background: rgba(255, 255, 255, 0.1);
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background: rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: rgba(255, 255, 255, 0.5);
            }
        """)

        # إنشاء widget المحتوى مع خلفية شفافة
        info_widget = QWidget()
        info_widget.setStyleSheet("""
            QWidget {
                background: transparent;
                border: none;
            }
        """)
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(15)

        # إضافة أقسام المعلومات
        self.create_info_sections(info_layout)

        scroll_area.setWidget(info_widget)
        layout.addWidget(scroll_area)

        # إضافة أزرار التحكم
        self.create_control_buttons(layout)

        # تطبيق تصميم شريط العنوان المتطور
        self.apply_advanced_title_bar_styling()

    def apply_advanced_title_bar_styling(self):
        """تطبيق تصميم شريط العنوان المتطور مطابق للعملاء"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تطبيق تصميم شريط العنوان المتطور: {e}")

    def create_window_icon(self):
        """إنشاء أيقونة النافذة"""
        try:
            from PyQt5.QtGui import QIcon, QPixmap, QPainter, QColor
            pixmap = QPixmap(32, 32)
            pixmap.fill(QColor(79, 70, 229))
            return QIcon(pixmap)
        except:
            return QIcon()

    def customize_title_bar(self):
        """تخصيص شريط العنوان مطابق لنافذة الحذف"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            print(f"تحذير: فشل في تخصيص شريط العنوان: {e}")

    @staticmethod
    def get_reference_styling():
        """الحصول على التصميم المرجعي للنوافذ"""
        return """
            QDialog {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #0F172A, stop:0.08 #1E293B, stop:0.15 #334155,
                    stop:0.25 #475569, stop:0.35 #1E40AF, stop:0.45 #2563EB,
                    stop:0.55 #3B82F6, stop:0.65 #60A5FA, stop:0.72 #8B5CF6,
                    stop:0.8 #7C3AED, stop:0.88 #6D28D9, stop:0.95 #5B21B6,
                    stop:1 #4C1D95);
                border: 4px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
            }
            QDialog::title {
                color: #ffffff;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            }
        """

    def create_info_sections(self, layout):
        """إنشاء أقسام المعلومات مطابق تماماً للعملاء"""
        # قسم المعلومات الأساسية
        basic_info = [
            ("🏦 رقم القسط", self.installment.installment_number or "غير محدد"),
            ("🧑‍💼 العميل", self.installment.client.name if self.installment.client else "غير محدد"),
            ("📆 تاريخ الإنشاء", self.installment.date.strftime("%Y-%m-%d") if self.installment.date else "غير محدد"),
            ("⏳ موعد الاستحقاق", self.installment.due_date.strftime("%Y-%m-%d") if self.installment.due_date else "غير محدد")
        ]
        self.add_info_section(layout, "📋 المعلومات الأساسية", basic_info)

        # قسم المعلومات المالية
        total_amount = f"{self.installment.total_amount:.2f} ج.م" if self.installment.total_amount else "0.00 ج.م"
        paid_amount = f"{self.installment.paid_amount:.2f} ج.م" if self.installment.paid_amount else "0.00 ج.م"
        remaining_amount = (self.installment.total_amount or 0) - (self.installment.paid_amount or 0)
        remaining_text = f"{remaining_amount:.2f} ج.م"

        financial_info = [
            ("💎 إجمالي المبلغ", total_amount),
            ("💵 المبلغ المسدد", paid_amount),
            ("💰 المبلغ المتبقي", remaining_text),
            ("📊 نسبة السداد", f"{((self.installment.paid_amount or 0) / (self.installment.total_amount or 1) * 100):.1f}%")
        ]
        self.add_info_section(layout, "💰 المعلومات المالية", financial_info)

        # قسم الحالة والملاحظات
        status_map = {
            'pending': '⏰ معلق',
            'paid': '✅ مسدد',
            'overdue': '🚨 متأخر',
            'cancelled': '🚫 ملغي'
        }
        status_text = status_map.get(self.installment.status, '❓ غير محدد')

        status_info = [
            ("🎯 حالة الدفع", status_text),
            ("📝 الملاحظات", self.installment.notes or "لا توجد ملاحظات")
        ]
        self.add_info_section(layout, "📝 الحالة والملاحظات", status_info)

        # قسم الملاحظات والتفاصيل الإضافية (مطابق للعملاء)
        self.add_info_section(layout, "📝 ملاحظات وتفاصيل إضافية", [
            ("📝 الملاحظات", self.installment.notes or "لا توجد ملاحظات"),
            ("🔍 معلومات إضافية", self.get_additional_info()),
            ("📋 ملخص الحساب", self.get_account_summary())
        ])

    def add_info_section(self, layout, title, info_list):
        """إضافة قسم معلومات مطابق تماماً للعملاء"""
        # إطار القسم مطابق للعملاء
        section_frame = QFrame()
        section_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.08),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(241, 245, 249, 0.10),
                    stop:1 rgba(226, 232, 240, 0.08));
                border: 2px solid rgba(255, 255, 255, 0.15);
                border-radius: 15px;
                margin: 8px 0px;
                padding: 0px;
            }
        """)

        section_layout = QVBoxLayout(section_frame)
        section_layout.setSpacing(12)
        section_layout.setContentsMargins(15, 15, 15, 15)

        # عنوان القسم
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(99, 102, 241, 0.7),
                    stop:1 rgba(139, 92, 246, 0.8));
                border: 2px solid rgba(255, 255, 255, 0.25);
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        title_label.setAlignment(Qt.AlignCenter)
        section_layout.addWidget(title_label)

        # إضافة المعلومات
        for label_text, value_text in info_list:
            self.add_info_row_to_section(section_layout, label_text, value_text)

        layout.addWidget(section_frame)

    def get_value_color(self, label, value):
        """تحديد لون القيمة حسب المحتوى - مطابق تماماً للعملاء"""
        try:
            # ألوان للرصيد والمبالغ المالية
            if "مبلغ" in label or "ج.م" in value:
                if "0" in value or "0.0" in value:
                    return "#E2E8F0"  # رمادي فاتح للصفر
                elif any(pos in value for pos in ["دائن", "💚", "🟢"]):
                    return "#00FF7F"  # أخضر نيون للموجب
                elif any(neg in value for neg in ["مدين", "🔴"]):
                    return "#FF6B6B"  # أحمر نيون للسالب
                else:
                    return "#FFFFFF"  # أبيض نقي افتراضي

            # ألوان للحالات
            elif "حالة" in label:
                if any(good in value for good in ["مسدد", "✅", "ممتاز", "🌟"]):
                    return "#00FF7F"  # أخضر نيون للحالات الجيدة
                elif any(warn in value for warn in ["معلق", "⚠️", "متوسط", "⏰"]):
                    return "#FFD700"  # ذهبي للتحذيرات
                elif any(bad in value for bad in ["متأخر", "🚨", "ملغي", "🚫"]):
                    return "#FF6B6B"  # أحمر نيون للحالات السيئة
                else:
                    return "#00BFFF"  # أزرق سماوي للحالات العادية

            # ألوان للنسب المئوية
            elif "نسبة" in label or "%" in value:
                try:
                    percentage = float(value.replace('%', '').replace('نسبة السداد:', '').strip())
                    if percentage >= 100:
                        return "#00FF7F"  # أخضر للمكتمل
                    elif percentage >= 75:
                        return "#FFD700"  # ذهبي للعالي
                    elif percentage >= 50:
                        return "#FFA500"  # برتقالي للمتوسط
                    else:
                        return "#FF6B6B"  # أحمر للمنخفض
                except:
                    return "#FFFFFF"

            # ألوان للتواريخ
            elif "تاريخ" in label:
                return "#F0F8FF"  # أبيض مزرق للتواريخ

            # ألوان للملاحظات
            elif "ملاحظات" in label:
                if "لا توجد" in value:
                    return "#C0C0C0"  # فضي للفارغ
                else:
                    return "#F5F5DC"  # بيج للنصوص

            # لون افتراضي
            else:
                return "#FFFFFF"  # أبيض نقي افتراضي

        except Exception as e:
            print(f"خطأ في تحديد لون القيمة: {e}")
            return "#FFFFFF"

    def add_info_row_to_section(self, layout, label_text, value_text):
        """إضافة صف معلومات مطابق تماماً للعملاء"""
        row_frame = QFrame()
        row_frame.setStyleSheet("""
            QFrame {
                background: transparent;
                border: none;
                margin: 2px 0;
            }
        """)
        row_layout = QHBoxLayout(row_frame)
        row_layout.setContentsMargins(0, 5, 0, 5)
        row_layout.setSpacing(15)

        # العنوان مطابق تماماً للعملاء
        label = QLabel(label_text)
        label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 14px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 12px 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(59, 130, 246, 0.8),
                    stop:0.5 rgba(99, 102, 241, 0.7),
                    stop:1 rgba(139, 92, 246, 0.8));
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                min-width: 180px;
                max-width: 220px;
            }
        """)

        # القيمة مطابقة تماماً للعملاء
        value = QLabel(str(value_text))
        value.setStyleSheet(f"""
            QLabel {{
                color: {self.get_value_color(label_text, str(value_text))};
                font-size: 13px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 12px 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.12),
                    stop:0.3 rgba(248, 250, 252, 0.1),
                    stop:0.7 rgba(241, 245, 249, 0.08),
                    stop:1 rgba(226, 232, 240, 0.06));
                border: 2px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
            }}
        """)
        value.setWordWrap(True)

        row_layout.addWidget(label)
        row_layout.addWidget(value, 1)  # تمدد القيمة لتأخذ المساحة المتبقية

        layout.addWidget(row_frame)



    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم مطابقة للعملاء"""
        # إطار الأزرار
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.15),
                    stop:0.3 rgba(248, 250, 252, 0.12),
                    stop:0.7 rgba(226, 232, 240, 0.1),
                    stop:1 rgba(203, 213, 224, 0.08));
                border: 3px solid rgba(255, 255, 255, 0.3);
                border-radius: 15px;
                margin: 15px 5px 5px 5px;
                padding: 20px;
                min-height: 80px;
                max-height: 90px;
            }
        """)

        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        buttons_layout.setContentsMargins(10, 10, 10, 10)

        # زر الطباعة
        print_btn = QPushButton("🖨️ طباعة")
        print_btn.setMinimumHeight(50)
        print_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(print_btn, 'info', False)
        print_btn.clicked.connect(self.print_info)

        # زر التصدير إلى PDF
        export_btn = QPushButton("📄 تصدير PDF")
        export_btn.setMinimumHeight(50)
        export_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(export_btn, 'primary', False)
        export_btn.clicked.connect(self.export_to_pdf)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق")
        close_btn.setMinimumHeight(50)
        close_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(close_btn, 'danger', False)
        close_btn.clicked.connect(self.close)

        # زر إضافة ملاحظة (مطابق للعملاء)
        note_btn = QPushButton("📝 ملاحظة")
        note_btn.setMinimumHeight(50)
        note_btn.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)
        self.style_advanced_button(note_btn, 'emerald', False)
        note_btn.clicked.connect(self.add_note)

        # ترتيب الأزرار مطابق للعملاء: إغلاق، طباعة، تصدير، ملاحظة
        buttons_layout.addWidget(close_btn)  # زر الإغلاق في المقدمة
        buttons_layout.addWidget(print_btn)
        buttons_layout.addWidget(export_btn)
        buttons_layout.addWidget(note_btn)

        layout.addWidget(buttons_frame)

    def add_note(self):
        """فتح نافذة إضافة ملاحظة متطورة مطابقة للعملاء"""
        try:
            dialog = AddInstallmentNoteDialog(self, self.installment, self.parent_widget)
            if dialog.exec_() == QDialog.Accepted:
                # تحديث العرض في النافذة الحالية
                self.refresh_installment_info()

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة الملاحظات: {str(e)}")

    def refresh_installment_info(self):
        """تحديث معلومات القسط في النافذة الحالية"""
        try:
            # إعادة تحميل بيانات القسط من قاعدة البيانات
            if self.parent_widget and hasattr(self.parent_widget, 'session'):
                self.parent_widget.session.refresh(self.installment)

            # إعادة إنشاء المحتوى
            self.setup_ui()

        except Exception as e:
            print(f"خطأ في تحديث معلومات القسط: {e}")

    def get_additional_info(self):
        """الحصول على معلومات إضافية للقسط"""
        try:
            info_parts = []

            # معلومات الحالة
            if self.installment.status:
                status_names = {
                    'pending': 'معلق',
                    'paid': 'مسدد',
                    'overdue': 'متأخر',
                    'cancelled': 'ملغي'
                }
                info_parts.append(f"الحالة: {status_names.get(self.installment.status, 'غير محدد')}")

            # معلومات التواريخ
            if self.installment.date and self.installment.due_date:
                from datetime import datetime
                days_diff = (self.installment.due_date - self.installment.date).days
                info_parts.append(f"مدة الاستحقاق: {days_diff} يوم")

            # معلومات المبالغ
            if self.installment.total_amount and self.installment.paid_amount:
                remaining = self.installment.total_amount - self.installment.paid_amount
                if remaining > 0:
                    info_parts.append(f"متبقي: {remaining:.2f} ج.م")
                elif remaining == 0:
                    info_parts.append("مسدد بالكامل")

            return " | ".join(info_parts) if info_parts else "لا توجد معلومات إضافية"

        except Exception as e:
            return f"خطأ في جلب المعلومات: {str(e)}"

    def get_account_summary(self):
        """الحصول على ملخص حساب القسط"""
        try:
            summary_parts = []

            # إجمالي المبلغ
            if self.installment.total_amount:
                summary_parts.append(f"الإجمالي: {self.installment.total_amount:.2f} ج.م")

            # المبلغ المسدد
            if self.installment.paid_amount:
                summary_parts.append(f"المسدد: {self.installment.paid_amount:.2f} ج.م")

            # النسبة المئوية للسداد
            if self.installment.total_amount and self.installment.total_amount > 0:
                percentage = (self.installment.paid_amount or 0) / self.installment.total_amount * 100
                summary_parts.append(f"نسبة السداد: {percentage:.1f}%")

            return " | ".join(summary_parts) if summary_parts else "لا يوجد ملخص متاح"

        except Exception as e:
            return f"خطأ في ملخص الحساب: {str(e)}"

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور على الأزرار مطابق تماماً للعملاء"""
        try:
            # استخدام دالة التصميم من الوالد إذا كانت متاحة
            if self.parent_widget and hasattr(self.parent_widget, 'style_advanced_button'):
                self.parent_widget.style_advanced_button(button, button_type, has_menu)
            else:
                # تصميم متطور مطابق للنموذج المرجعي
                color_schemes = {
                    'info': {
                        'base': '#3B82F6',
                        'hover': '#2563EB',
                        'pressed': '#1D4ED8',
                        'shadow': 'rgba(59, 130, 246, 0.4)'
                    },
                    'primary': {
                        'base': '#4F46E5',
                        'hover': '#4338CA',
                        'pressed': '#3730A3',
                        'shadow': 'rgba(79, 70, 229, 0.4)'
                    },
                    'danger': {
                        'base': '#EF4444',
                        'hover': '#DC2626',
                        'pressed': '#B91C1C',
                        'shadow': 'rgba(239, 68, 68, 0.4)'
                    },
                    'emerald': {
                        'base': '#10B981',
                        'hover': '#059669',
                        'pressed': '#047857',
                        'shadow': 'rgba(16, 185, 129, 0.4)'
                    }
                }

                colors = color_schemes.get(button_type, color_schemes['info'])

                button.setStyleSheet(f"""
                    QPushButton {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {colors['base']}, stop:1 {colors['hover']});
                        color: white;
                        border: 2px solid {colors['base']};
                        border-radius: 12px;
                        padding: 12px 20px;
                        font-size: 14px;
                        font-weight: bold;
                        font-family: 'Segoe UI', 'Roboto', sans-serif;
                        box-shadow: 0 4px 8px {colors['shadow']};
                    }}
                    QPushButton:hover {{
                        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                            stop:0 {colors['hover']}, stop:1 {colors['pressed']});
                        border: 2px solid {colors['hover']};
                        transform: translateY(-2px);
                        box-shadow: 0 6px 12px {colors['shadow']};
                    }}
                    QPushButton:pressed {{
                        background: {colors['pressed']};
                        border: 2px solid {colors['pressed']};
                        transform: translateY(0px);
                        box-shadow: 0 2px 4px {colors['shadow']};
                    }}
                """)
        except Exception as e:
            print(f"خطأ في تصميم الزر: {e}")

    def print_info(self):
        """طباعة معلومات القسط"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtGui import QPainter, QFont
            from PyQt5.QtCore import QRect

            printer = QPrinter()
            dialog = QPrintDialog(printer, self)

            if dialog.exec_() == QPrintDialog.Accepted:
                painter = QPainter(printer)

                # إعداد الخط
                font = QFont("Arial", 12)
                painter.setFont(font)

                # طباعة المعلومات
                y = 100
                painter.drawText(100, y, f"معلومات القسط: {self.installment.installment_number}")
                y += 50
                painter.drawText(100, y, f"العميل: {self.installment.client.name if self.installment.client else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"إجمالي المبلغ: {self.installment.total_amount:.2f} ج.م")
                y += 50
                painter.drawText(100, y, f"المبلغ المسدد: {self.installment.paid_amount:.2f} ج.م")
                y += 50
                remaining = (self.installment.total_amount or 0) - (self.installment.paid_amount or 0)
                painter.drawText(100, y, f"المبلغ المتبقي: {remaining:.2f} ج.م")

                painter.end()

                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح", "تم طباعة معلومات القسط بنجاح!")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في الطباعة: {str(e)}")

    def export_to_pdf(self):
        """تصدير معلومات القسط إلى PDF"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtGui import QPainter, QFont

            # اختيار مكان الحفظ
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف PDF",
                f"installment_{self.installment.installment_number}.pdf",
                "PDF Files (*.pdf)"
            )

            if file_path:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)

                painter = QPainter(printer)

                # إعداد الخط
                font = QFont("Arial", 12)
                painter.setFont(font)

                # كتابة المعلومات
                y = 100
                painter.drawText(100, y, f"معلومات القسط: {self.installment.installment_number}")
                y += 50
                painter.drawText(100, y, f"العميل: {self.installment.client.name if self.installment.client else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"تاريخ الإنشاء: {self.installment.date.strftime('%Y-%m-%d') if self.installment.date else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"موعد الاستحقاق: {self.installment.due_date.strftime('%Y-%m-%d') if self.installment.due_date else 'غير محدد'}")
                y += 50
                painter.drawText(100, y, f"إجمالي المبلغ: {self.installment.total_amount:.2f} ج.م")
                y += 50
                painter.drawText(100, y, f"المبلغ المسدد: {self.installment.paid_amount:.2f} ج.م")
                y += 50
                remaining = (self.installment.total_amount or 0) - (self.installment.paid_amount or 0)
                painter.drawText(100, y, f"المبلغ المتبقي: {remaining:.2f} ج.م")
                y += 50
                status_map = {
                    'pending': 'معلق',
                    'paid': 'مسدد',
                    'overdue': 'متأخر',
                    'cancelled': 'ملغي'
                }
                status_text = status_map.get(self.installment.status, 'غير محدد')
                painter.drawText(100, y, f"حالة الدفع: {status_text}")

                if self.installment.notes:
                    y += 50
                    painter.drawText(100, y, f"الملاحظات: {self.installment.notes}")

                painter.end()

                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "نجح", f"تم تصدير معلومات القسط إلى:\n{file_path}")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في التصدير: {str(e)}")


class AddInstallmentNoteDialog(QDialog):
    """نافذة ملاحظات بسيطة جداً مطابقة للعملاء"""

    def __init__(self, parent=None, installment=None, parent_widget=None):
        super().__init__(parent)
        self.installment = installment
        self.parent_widget = parent_widget
        self.setup_ui()

    def setup_ui(self):
        """إعداد نافذة بسيطة جداً مطابقة للعملاء"""
        installment_name = f"القسط {self.installment.installment_number}" if self.installment and self.installment.installment_number else "قسط"
        self.setWindowTitle(f"📝 {installment_name}")
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.WindowCloseButtonHint)
        self.setModal(True)
        self.resize(450, 350)

        # تخصيص شريط العنوان الخارجي ليكون أسود
        self.customize_title_bar()

        # خلفية متطابقة مع النموذج المرجعي
        self.setStyleSheet(InstallmentInfoDialog.get_reference_styling())

        # تخطيط بسيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # عنوان النافذة
        title_label = QLabel("📝 إضافة ملاحظة للقسط")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 18px;
                font-weight: bold;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                padding: 10px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(79, 70, 229, 0.8),
                    stop:0.5 rgba(139, 92, 246, 0.7),
                    stop:1 rgba(124, 58, 237, 0.8));
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        layout.addWidget(title_label)

        # حقل النص
        self.notes_edit = QTextEdit()
        self.notes_edit.setPlaceholderText("أدخل الملاحظة هنا...")
        if self.installment and self.installment.notes:
            self.notes_edit.setPlainText(self.installment.notes)

        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.95),
                    stop:0.2 rgba(248, 250, 252, 0.98),
                    stop:0.4 rgba(241, 245, 249, 0.95),
                    stop:0.6 rgba(248, 250, 252, 0.98),
                    stop:0.8 rgba(255, 255, 255, 0.95),
                    stop:1 rgba(226, 232, 240, 0.9));
                border: 3px solid rgba(139, 92, 246, 0.7);
                border-radius: 10px;
                padding: 12px;
                font-size: 13px;
                font-weight: bold;
                color: #1f2937;
                selection-background-color: rgba(96, 165, 250, 0.3);
                selection-color: #1f2937;
            }
            QTextEdit:focus {
                border: 4px solid rgba(96, 165, 250, 0.95);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(240, 249, 255, 0.98),
                    stop:0.2 rgba(224, 242, 254, 0.95),
                    stop:0.4 rgba(186, 230, 253, 0.9),
                    stop:0.6 rgba(224, 242, 254, 0.95),
                    stop:0.8 rgba(240, 249, 255, 0.98),
                    stop:1 rgba(255, 255, 255, 0.95));
            }
        """)
        layout.addWidget(self.notes_edit)

        # الأزرار
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر الحفظ
        save_btn = QPushButton("💾 حفظ")
        save_btn.setMinimumHeight(40)
        save_btn.clicked.connect(self.save_note)
        self.style_button(save_btn, 'emerald')

        # زر الإلغاء
        cancel_btn = QPushButton("❌ إلغاء")
        cancel_btn.setMinimumHeight(40)
        cancel_btn.clicked.connect(self.reject)
        self.style_button(cancel_btn, 'danger')

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(save_btn)

        layout.addLayout(buttons_layout)

    def customize_title_bar(self):
        """تخصيص شريط العنوان الخارجي ليكون أسود"""
        try:
            from ui.title_bar_utils import TitleBarStyler
            TitleBarStyler.apply_advanced_title_bar_styling(self)
        except Exception as e:
            pass

    def style_button(self, button, button_type):
        """تطبيق تصميم على الأزرار"""
        colors = {
            'emerald': '#10b981',
            'danger': '#ef4444'
        }
        color = colors.get(button_type, '#6b7280')
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
        """)

    def save_note(self):
        """حفظ الملاحظة"""
        try:
            if self.installment:
                # تحديث الملاحظة
                self.installment.notes = self.notes_edit.toPlainText().strip() or None

                # حفظ في قاعدة البيانات
                if self.parent_widget and hasattr(self.parent_widget, 'session'):
                    self.parent_widget.session.commit()

                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.information(self, "تم", "تم حفظ الملاحظة بنجاح!")
                self.accept()
            else:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.warning(self, "خطأ", "لا يمكن حفظ الملاحظة - القسط غير محدد")

        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            QMessageBox.critical(self, "خطأ", f"حدث خطأ في حفظ الملاحظة: {str(e)}")
