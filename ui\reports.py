from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QPushButton,
                            QLabel, QTableWidget, QTableWidgetItem,
                            QHeaderView, QTabWidget, QDateEdit, QComboBox,
                            QGroupBox, QFormLayout, QTextBrowser, QFileDialog,
                            QMessageBox, QFrame, QSizePolicy, QMenu, QAction,
                            QGridLayout)
from PyQt5.QtCore import QDate, Qt
from PyQt5.QtGui import QColor, QFont
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
import csv
import json

from database import Sale, Purchase, SaleItem, Inventory, Client, Supplier, Expense, Revenue, Project
from utils import show_error_message, qdate_to_datetime, format_currency
from ui.unified_styles import StyledLabel

class ReportsWidget(QWidget):
    """واجهة التقارير والتحليل المالي"""

    def __init__(self, session):
        super().__init__()
        self.session = session
        self.init_ui()

    def init_ui(self):
        # إنشاء التخطيط الرئيسي مطابق تماماً لباقي البرنامج
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية
        main_layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة العنوان الرئيسي مطابق للفواتير
        title_label = QLabel("📊 إدارة التقارير المتطورة - نظام شامل ومتقدم لإدارة التقارير مع أدوات احترافية للبحث والتحليل والتقارير")
        title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))  # خط أكبر وأوضح
        title_label.setAlignment(Qt.AlignCenter)  # توسيط النص في المنتصف
        title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #334155,
                    stop:0.3 #475569, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #60A5FA, stop:0.7 #8B5CF6, stop:0.8 #7C3AED,
                    stop:0.9 #6D28D9, stop:1 #5B21B6);
                border: 3px solid #000000;
                border-radius: 10px;
                padding: 4px 10px;
                margin: 2px;
                font-weight: bold;
                max-height: 40px;
                min-height: 40px;
            }
        """)
        main_layout.addWidget(title_label)

        # إزالة الإطار العلوي والاكتفاء بالتبويبات فقط

        # إنشاء تبويبات للتقارير المختلفة مع تصميم محسن ومطابق لباقي البرنامج
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 3px solid #000000;
                border-radius: 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #ffffff,
                    stop:0.3 #f8fafc,
                    stop:0.7 #e2e8f0,
                    stop:1 #cbd5e0);
                margin-top: 2px;
            }
            QTabBar::tab {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                border: 4px solid #000000;
                border-bottom: 4px solid #000000;
                border-radius: 12px;
                padding: 8px 32px;
                margin: 2px;
                font-size: 20px;
                font-weight: bold;
                min-width: 136px;
                max-width: 136px;
                min-height: 30px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2),
                           0 1px 3px rgba(0, 0, 0, 0.1),
                           0 -2px 5px rgba(0, 0, 0, 0.1);
            }
            QTabBar::tab:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 6px solid #2563EB;
                border-bottom: 6px solid #2563EB;
                margin-top: -1px;
                padding: 9px 32px;
                font-size: 20px;
                font-weight: bold;
                min-width: 136px;
                max-width: 136px;
                min-height: 30px;
                max-height: 40px;
                text-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);
                box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4),
                           0 3px 12px rgba(0, 0, 0, 0.3),
                           0 -3px 8px rgba(37, 99, 235, 0.3);
                border-radius: 12px;
            }
            QTabBar::tab:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.2 #1e293b, stop:0.3 #0f172a,
                    stop:0.4 #1e40af, stop:0.6 #1d4ed8, stop:0.7 #0f172a,
                    stop:0.8 #1e293b, stop:1 #334155);
                border: 4px solid #3B82F6;
                border-bottom: 4px solid #3B82F6;
                color: #ffffff;
                font-weight: 800;
                font-size: 20px;
                min-width: 136px;
                max-width: 136px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.45);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.35),
                           0 2px 6px rgba(0, 0, 0, 0.2),
                           0 -2px 5px rgba(59, 130, 246, 0.25);
                border-radius: 12px;
            }
        """)

        # تبويب تقرير العملاء
        self.clients_tab = QWidget()
        self.setup_clients_tab()
        self.tabs.addTab(self.clients_tab, "🤝 العملاء")

        # تبويب تقرير الموردين
        self.suppliers_tab = QWidget()
        self.setup_suppliers_tab()
        self.tabs.addTab(self.suppliers_tab, "🏭 الموردين")

        # تبويب تقرير المشاريع
        self.projects_tab = QWidget()
        self.setup_projects_tab()
        self.tabs.addTab(self.projects_tab, "🏗️ المشاريع")

        # تبويب تقرير الإيرادات
        self.revenues_tab = QWidget()
        self.setup_revenues_tab()
        self.tabs.addTab(self.revenues_tab, "💰 الإيرادات")

        # تبويب تقرير المصروفات
        self.expenses_tab = QWidget()
        self.setup_expenses_tab()
        self.tabs.addTab(self.expenses_tab, "💸 المصروفات")

        # تبويب تقرير المشتريات
        self.purchases_tab = QWidget()
        self.setup_purchases_tab()
        self.tabs.addTab(self.purchases_tab, "🛒 المشتريات")

        # تبويب تقرير المبيعات
        self.sales_tab = QWidget()
        self.setup_sales_tab()
        self.tabs.addTab(self.sales_tab, "📊 المبيعات")

        # تبويب تقرير المخزون
        self.inventory_tab = QWidget()
        self.setup_inventory_tab()
        self.tabs.addTab(self.inventory_tab, "📦 المخزن")

        # تبويب تقرير الأرباح والخسائر
        self.profit_loss_tab = QWidget()
        self.setup_profit_loss_tab()
        self.tabs.addTab(self.profit_loss_tab, "💰 الأرباح والخسائر")

        main_layout.addWidget(self.tabs, 1)  # إعطاء التبويبات أولوية في التمدد
        self.setLayout(main_layout)

    def setup_sales_tab(self):
        """إعداد تبويب تقرير المبيعات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن مطابق للتصميم الموحد
        filter_group = QGroupBox("🛒 فلاتر تقرير المبيعات")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.sales_period_combo = QComboBox()
        self.sales_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.sales_period_combo.currentIndexChanged.connect(self.on_sales_period_changed)
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.sales_period_combo, 'primary')
        filter_layout.addRow(period_label, self.sales_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية مع التصميم الموحد
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.sales_start_date = QDateEdit()
        self.sales_start_date.setCalendarPopup(True)
        self.sales_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.sales_start_date.setEnabled(False)
        self.sales_start_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 35px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.3 #cbd5e1, stop:0.7 #94a3b8, stop:1 #64748b);
                border: 3px solid #3B82F6;
                color: #0f172a;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                color: #64748b;
                border: 3px solid #cbd5e1;
            }
        """)

        # تسمية وحقل تاريخ النهاية مع التصميم الموحد
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.sales_end_date = QDateEdit()
        self.sales_end_date.setCalendarPopup(True)
        self.sales_end_date.setDate(QDate.currentDate())
        self.sales_end_date.setEnabled(False)
        self.sales_end_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 35px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.3 #cbd5e1, stop:0.7 #94a3b8, stop:1 #64748b);
                border: 3px solid #3B82F6;
                color: #0f172a;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                color: #64748b;
                border: 3px solid #cbd5e1;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.sales_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.sales_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # فلتر العميل مع تصميم محسن
        client_label = QLabel("👤 العميل:")
        client_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.sales_client_combo = QComboBox()
        self.sales_client_combo.addItem("جميع العملاء", None)
        clients = self.session.query(Client).order_by(Client.name).all()
        for client in clients:
            self.sales_client_combo.addItem(client.name, client.id)
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.sales_client_combo, 'emerald')
        filter_layout.addRow(client_label, self.sales_client_combo)

        # فلتر الحالة مع تصميم محسن
        status_label = QLabel("🔄 الحالة:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.sales_status_combo = QComboBox()
        self.sales_status_combo.addItem("جميع الحالات", None)
        self.sales_status_combo.addItem("في الانتظار", "pending")
        self.sales_status_combo.addItem("مكتملة", "completed")
        self.sales_status_combo.addItem("ملغية", "cancelled")
        self.sales_status_combo.addItem("مرتجعة", "returned")
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.sales_status_combo, 'warning')
        filter_layout.addRow(status_label, self.sales_status_combo)

        # تطبيق التصميم الموحد على جميع القوائم المنسدلة في التقارير
        self.apply_unified_combobox_styles()

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.sales_apply_button = QPushButton("📊 تطبيق التقرير")
        self.style_advanced_button(self.sales_apply_button, 'emerald')
        self.sales_apply_button.clicked.connect(self.generate_sales_report)

        self.sales_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.sales_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        sales_export_menu = QMenu(self)
        sales_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.sales_table, "تقرير_المبيعات"))
        sales_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.sales_table, "تقرير المبيعات"))
        sales_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.sales_table, "تقرير_المبيعات"))
        sales_export_menu.addAction(csv_action)

        self.sales_export_button.setMenu(sales_export_menu)

        self.sales_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.sales_refresh_button, 'modern_teal')
        self.sales_refresh_button.clicked.connect(self.generate_sales_report)

        buttons_layout.addWidget(self.sales_apply_button)
        buttons_layout.addWidget(self.sales_export_button)
        buttons_layout.addWidget(self.sales_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن مطابق للتصميم الموحد
        results_group = QGroupBox("🛒 نتائج تقرير المبيعات")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # إضافة ملخص إحصائي متطور مطابق للتصميم الموحد
        stats_layout = QHBoxLayout()

        # عدد الفواتير
        self.sales_count_label = StyledLabel("عدد الفواتير: 0", "info")
        stats_layout.addWidget(self.sales_count_label.label)

        # إجمالي المبيعات
        self.sales_total_label = StyledLabel("إجمالي المبيعات: 0.00 ج.م", "success")
        stats_layout.addWidget(self.sales_total_label.label)

        # متوسط المبيعات
        self.sales_avg_label = StyledLabel("متوسط المبيعات: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.sales_avg_label.label)

        results_layout.addLayout(stats_layout)
        # جدول المبيعات مطابق لباقي البرنامج
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(7)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم الفاتورة",
            "👨‍💼 العميل",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "💳 طريقة الدفع",
            "🎯 الحالة"
        ]
        self.sales_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
                font-weight: bold !important;
                font-size: 14px !important;
                transform: translateY(-1px) !important;
            }

            QTableWidget::item:selected:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(79, 172, 254, 0.9),
                    stop:0.5 rgba(0, 242, 254, 0.9),
                    stop:1 rgba(102, 126, 234, 0.9)) !important;
                border: 4px solid rgba(255, 255, 255, 0.9) !important;
                border-left: 6px solid #ffd700 !important;
                border-right: 6px solid #ffd700 !important;
                box-shadow: 0px 8px 20px rgba(102, 126, 234, 0.5) !important;
        """)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.sales_table.verticalHeader().setDefaultSectionSize(45)
        self.sales_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header = self.sales_table.horizontalHeader()
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.sales_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.sales_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.sales_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.sales_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.sales_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def sales_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.sales_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.sales_table, event)

        self.sales_table.wheelEvent = sales_wheelEvent

        # عنوان الجدول مع تصميم محسن مطابق للتصميم الموحد
        table_title = QLabel("📊 تفاصيل المبيعات")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                padding: 15px 25px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                border: 4px solid #000000;
                border-radius: 12px;
                margin: 8px 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
        """)
        results_layout.addWidget(table_title)
        results_layout.addWidget(self.sales_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)  # تقليل المسافة بين الفلاتر والنتائج
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية

        self.sales_tab.setLayout(layout)

    def setup_purchases_tab(self):
        """إعداد تبويب تقرير المشتريات مع تنسيق محسن لاستغلال مساحة أكبر للجدول"""
        # التخطيط الرئيسي أفقي لاستغلال المساحة بشكل أفضل
        main_layout = QHBoxLayout()

        # الجانب الأيسر للفلاتر (عرض أقل)
        left_panel = QWidget()
        left_panel.setFixedWidth(350)  # عرض ثابت للفلاتر
        left_layout = QVBoxLayout(left_panel)

        # مجموعة فلاتر التقرير مع تصميم مضغوط
        filter_group = QGroupBox("🛒 فلاتر المشتريات")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 12px;
                margin-top: 10px;
                padding-top: 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 8px 16px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(15)
        filter_layout.setContentsMargins(20, 30, 20, 20)

        # فلتر الفترة الزمنية مع تصميم موسع
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
                min-height: 25px;
            }
        """)

        self.purchases_period_combo = QComboBox()
        self.purchases_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.purchases_period_combo.currentIndexChanged.connect(self.on_purchases_period_changed)
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.purchases_period_combo, 'primary')
        filter_layout.addRow(period_label, self.purchases_period_combo)

        # حقول تاريخ البداية والنهاية مع تخطيط شبكي (2x2)
        dates_grid_layout = QGridLayout()
        dates_grid_layout.setSpacing(10)

        # تسمية وحقل تاريخ البداية في الصف الأول - مطابقة للمورد والحالة
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.purchases_start_date = QDateEdit()
        self.purchases_start_date.setCalendarPopup(True)
        self.purchases_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.purchases_start_date.setEnabled(False)
        self.purchases_start_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e293b, stop:0.3 #334155, stop:0.7 #475569, stop:1 #64748b);
                color: #ffffff;
                min-height: 35px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:0.3 #3b82f6, stop:0.7 #60a5fa, stop:1 #93c5fd);
                border: 3px solid #3B82F6;
                color: #ffffff;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #374151, stop:0.5 #4b5563, stop:1 #6b7280);
                color: #9ca3af;
                border: 3px solid #6b7280;
            }
            QDateEdit::drop-down {
                border: none;
                background: transparent;
            }
            QDateEdit::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
            }
        """)

        # تسمية وحقل تاريخ النهاية في الصف الثاني - مطابقة للمورد والحالة
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.purchases_end_date = QDateEdit()
        self.purchases_end_date.setCalendarPopup(True)
        self.purchases_end_date.setDate(QDate.currentDate())
        self.purchases_end_date.setEnabled(False)
        self.purchases_end_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #1e293b, stop:0.3 #334155, stop:0.7 #475569, stop:1 #64748b);
                color: #ffffff;
                min-height: 35px;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2563eb, stop:0.3 #3b82f6, stop:0.7 #60a5fa, stop:1 #93c5fd);
                border: 3px solid #3B82F6;
                color: #ffffff;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #374151, stop:0.5 #4b5563, stop:1 #6b7280);
                color: #9ca3af;
                border: 3px solid #6b7280;
            }
            QDateEdit::drop-down {
                border: none;
                background: transparent;
            }
            QDateEdit::down-arrow {
                image: none;
                border: none;
                width: 0px;
                height: 0px;
            }
        """)

        # ترتيب العناصر في الشبكة (2x2) مع تحسين العرض
        # الصف الأول: تسمية "من" وحقل تاريخ البداية
        dates_grid_layout.addWidget(start_label, 0, 0)
        dates_grid_layout.addWidget(self.purchases_start_date, 0, 1)

        # الصف الثاني: تسمية "إلى" وحقل تاريخ النهاية
        dates_grid_layout.addWidget(end_label, 1, 0)
        dates_grid_layout.addWidget(self.purchases_end_date, 1, 1)

        # تحسين نسب الأعمدة - العمود الثاني أوسع للحقول
        dates_grid_layout.setColumnStretch(0, 1)  # عمود التسميات
        dates_grid_layout.setColumnStretch(1, 2)  # عمود الحقول أوسع

        # إنشاء widget للشبكة وإضافته للتخطيط الرئيسي
        dates_widget = QWidget()
        dates_widget.setLayout(dates_grid_layout)
        filter_layout.addRow("", dates_widget)

        # فلتر المورد مع تصميم محسن
        supplier_label = QLabel("🏭 المورد:")
        supplier_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.purchases_supplier_combo = QComboBox()
        self.purchases_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.purchases_supplier_combo.addItem(supplier.name, supplier.id)
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.purchases_supplier_combo, 'primary')
        filter_layout.addRow(supplier_label, self.purchases_supplier_combo)

        # فلتر الحالة مع تصميم محسن
        status_label = QLabel("🔄 الحالة:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.purchases_status_combo = QComboBox()
        self.purchases_status_combo.addItem("جميع الحالات", None)
        self.purchases_status_combo.addItem("في الانتظار", "pending")
        self.purchases_status_combo.addItem("مستلم جزئياً", "partially_received")
        self.purchases_status_combo.addItem("مستلم بالكامل", "completed")
        self.purchases_status_combo.addItem("ملغي", "cancelled")
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.purchases_status_combo, 'primary')
        filter_layout.addRow(status_label, self.purchases_status_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.purchases_apply_button = QPushButton("🛒 تطبيق التقرير")
        self.style_advanced_button(self.purchases_apply_button, 'primary')
        self.purchases_apply_button.clicked.connect(self.generate_purchases_report)

        self.purchases_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.purchases_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        purchases_export_menu = QMenu(self)
        purchases_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.purchases_table, "تقرير_المشتريات"))
        purchases_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.purchases_table, "تقرير المشتريات"))
        purchases_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.purchases_table, "تقرير_المشتريات"))
        purchases_export_menu.addAction(csv_action)

        self.purchases_export_button.setMenu(purchases_export_menu)

        self.purchases_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.purchases_refresh_button, 'modern_teal')
        self.purchases_refresh_button.clicked.connect(self.generate_purchases_report)

        buttons_layout.addWidget(self.purchases_apply_button)
        buttons_layout.addWidget(self.purchases_export_button)
        buttons_layout.addWidget(self.purchases_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # جعل مجموعة الفلاتر تستغل كامل المساحة المتاحة
        filter_group.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        left_layout.addWidget(filter_group)

        # الجانب الأيمن للنتائج والجدول (مساحة أكبر)
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(10, 0, 0, 0)

        # مجموعة نتائج التقرير مع تصميم محسن للمساحة الكبيرة
        results_group = QGroupBox("🛒 نتائج المشتريات")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # إضافة ملخص إحصائي متطور مطابق للتصميم الموحد
        stats_layout = QHBoxLayout()

        # عدد أوامر الشراء
        self.purchases_count_label = StyledLabel("عدد أوامر الشراء: 0", "info")
        stats_layout.addWidget(self.purchases_count_label.label)

        # إجمالي المشتريات
        self.purchases_total_label = StyledLabel("إجمالي المشتريات: 0.00 ج.م", "success")
        stats_layout.addWidget(self.purchases_total_label.label)

        # إجمالي المدفوعات
        self.purchases_paid_label = StyledLabel("إجمالي المدفوعات: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.purchases_paid_label.label)

        results_layout.addLayout(stats_layout)

        # جدول المشتريات مطابق لباقي البرنامج
        self.purchases_table = QTableWidget()
        self.purchases_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم أمر الشراء",
            "🏢 المورد",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "🎯 الحالة"
        ]
        self.purchases_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم محسن للجدول لاستغلال المساحة الكبيرة
        self.purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 3px solid #000000;
                border-radius: 12px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 14px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 5px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 8px 10px;
                border: 1px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 25px;
                max-height: 35px;
                font-weight: bold;
                font-size: 13px;
                border-radius: 6px;
                margin: 1px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header = self.purchases_table.horizontalHeader()
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.purchases_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.purchases_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.purchases_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.purchases_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def purchases_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.purchases_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.purchases_table, event)

        self.purchases_table.wheelEvent = purchases_wheelEvent

        # تحسينات إضافية للجدول لاستغلال المساحة الكبيرة
        self.purchases_table.setMinimumHeight(400)  # ارتفاع أدنى للجدول
        self.purchases_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # تحسين عرض الأعمدة للمساحة الكبيرة
        header = self.purchases_table.horizontalHeader()
        header.setStretchLastSection(True)

        # عنوان الجدول مع تصميم مضغوط
        table_title = QLabel("🛒 تفاصيل المشتريات")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 14px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 15px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                border: 2px solid #000000;
                border-radius: 8px;
                margin: 4px 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout.addWidget(table_title)
        results_layout.addWidget(self.purchases_table)

        results_group.setLayout(results_layout)
        right_layout.addWidget(results_group)

        # إضافة الألواح إلى التخطيط الرئيسي الأفقي
        main_layout.addWidget(left_panel)
        main_layout.addWidget(right_panel)

        # تحسين المساحات للتخطيط الأفقي
        main_layout.setSpacing(5)
        main_layout.setContentsMargins(5, 5, 5, 5)

        self.purchases_tab.setLayout(main_layout)

    def setup_profit_loss_tab(self):
        """إعداد تبويب تقرير الأرباح والخسائر"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن مطابق للتصميم الموحد
        filter_group = QGroupBox("💰 فلاتر تقرير الأرباح والخسائر")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن مطابق للتصميم الموحد
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.profit_loss_period_combo = QComboBox()
        self.profit_loss_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.profit_loss_period_combo.currentIndexChanged.connect(self.on_profit_loss_period_changed)
        # تطبيق التصميم المتطور الموحد
        self.style_advanced_combobox(self.profit_loss_period_combo, 'primary')
        filter_layout.addRow(period_label, self.profit_loss_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن مطابق للتصميم الموحد
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية مع التصميم الموحد
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.profit_loss_start_date = QDateEdit()
        self.profit_loss_start_date.setCalendarPopup(True)
        self.profit_loss_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.profit_loss_start_date.setEnabled(False)
        self.profit_loss_start_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 35px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.3 #cbd5e1, stop:0.7 #94a3b8, stop:1 #64748b);
                border: 3px solid #3B82F6;
                color: #0f172a;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                color: #64748b;
                border: 3px solid #cbd5e1;
            }
        """)

        # تسمية وحقل تاريخ النهاية
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                font-weight: bold;
                color: #e74c3c;
                padding: 5px;
            }
        """)

        self.profit_loss_end_date = QDateEdit()
        self.profit_loss_end_date.setCalendarPopup(True)
        self.profit_loss_end_date.setDate(QDate.currentDate())
        self.profit_loss_end_date.setEnabled(False)
        self.profit_loss_end_date.setStyleSheet("""
            QDateEdit {
                border: 2px solid #e74c3c;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-weight: 500;
                background-color: white;
                color: #2c3e50;
                min-height: 25px;
            }
            QDateEdit:hover {
                border: 2px solid #c0392b;
                background-color: #f8f9fa;
            }
            QDateEdit:disabled {
                background-color: #ecf0f1;
                color: #7f8c8d;
                border: 2px solid #bdc3c7;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.profit_loss_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.profit_loss_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.profit_loss_apply_button = QPushButton("💰 تطبيق التقرير")
        self.style_advanced_button(self.profit_loss_apply_button, 'warning')
        self.profit_loss_apply_button.clicked.connect(self.generate_profit_loss_report)

        self.profit_loss_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.profit_loss_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        profit_loss_export_menu = QMenu(self)
        profit_loss_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.export_profit_loss_to_pdf())
        profit_loss_export_menu.addAction(pdf_action)

        html_action = QAction("🌐 حفظ كـ HTML", self)
        html_action.triggered.connect(lambda: self.save_profit_loss_html())
        profit_loss_export_menu.addAction(html_action)

        self.profit_loss_export_button.setMenu(profit_loss_export_menu)

        self.profit_loss_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.profit_loss_refresh_button, 'modern_teal')
        self.profit_loss_refresh_button.clicked.connect(self.generate_profit_loss_report)

        buttons_layout.addWidget(self.profit_loss_apply_button)
        buttons_layout.addWidget(self.profit_loss_export_button)
        buttons_layout.addWidget(self.profit_loss_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن مطابق للتصميم الموحد
        results_group = QGroupBox("💰 نتائج تقرير الأرباح والخسائر")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # إضافة ملخص إحصائي سريع
        quick_stats_layout = QHBoxLayout()

        # إجمالي الإيرادات
        self.profit_loss_revenue_label = StyledLabel("إجمالي الإيرادات: 0.00 ج.م", "success")
        quick_stats_layout.addWidget(self.profit_loss_revenue_label.label)

        # إجمالي المصروفات
        self.profit_loss_expenses_label = StyledLabel("إجمالي المصروفات: 0.00 ج.م", "danger")
        quick_stats_layout.addWidget(self.profit_loss_expenses_label.label)

        # صافي الربح/الخسارة
        self.profit_loss_net_label = StyledLabel("صافي الربح/الخسارة: 0.00 ج.م", "primary")
        quick_stats_layout.addWidget(self.profit_loss_net_label.label)

        results_layout.addLayout(quick_stats_layout)

        # تقرير الأرباح والخسائر المفصل مع تصميم محسن
        self.profit_loss_summary = QTextBrowser()
        self.profit_loss_summary.setMinimumHeight(500)
        self.profit_loss_summary.setStyleSheet("""
            QTextBrowser {
                border: 4px solid #000000;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                color: #1e293b;
                padding: 20px;
                line-height: 1.8;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QScrollBar:vertical {
                border: 3px solid #000000;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.5 #cbd5e1, stop:1 #94a3b8);
                width: 18px;
                border-radius: 9px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.5 #1e293b, stop:1 #0f172a);
                border: 2px solid #000000;
                border-radius: 7px;
                min-height: 25px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.5 #334155, stop:1 #1e293b);
            }
        """)

        results_layout.addWidget(self.profit_loss_summary)



        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)
        layout.setContentsMargins(2, 2, 2, 2)

        self.profit_loss_tab.setLayout(layout)

    def setup_inventory_tab(self):
        """إعداد تبويب تقرير المخزون"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن مطابق للتصميم الموحد
        filter_group = QGroupBox("📦 فلاتر تقرير المخزون")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفئة مع تصميم محسن
        category_label = QLabel("📂 الفئة:")
        category_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.inventory_category_combo = QComboBox()
        self.inventory_category_combo.addItem("جميع الفئات", None)
        categories = self.session.query(Inventory.category).distinct().all()
        for category in categories:
            if category[0]:
                self.inventory_category_combo.addItem(category[0], category[0])
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.inventory_category_combo, 'primary')
        filter_layout.addRow(category_label, self.inventory_category_combo)

        # فلتر المورد مع تصميم محسن
        supplier_label = QLabel("🏭 المورد:")
        supplier_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.inventory_supplier_combo = QComboBox()
        self.inventory_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.inventory_supplier_combo.addItem(supplier.name, supplier.id)
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.inventory_supplier_combo, 'primary')
        filter_layout.addRow(supplier_label, self.inventory_supplier_combo)

        # فلتر حالة المخزون مع تصميم محسن مطابق للتصميم الموحد
        status_label = QLabel("📊 حالة المخزون:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.inventory_status_combo = QComboBox()
        self.inventory_status_combo.addItem("جميع العناصر", "all")
        self.inventory_status_combo.addItem("عناصر منخفضة المخزون", "low_stock")
        self.inventory_status_combo.addItem("عناصر نفدت من المخزون", "out_of_stock")
        self.inventory_status_combo.addItem("عناصر متوفرة", "in_stock")
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.inventory_status_combo, 'primary')
        filter_layout.addRow(status_label, self.inventory_status_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.inventory_apply_button = QPushButton("📦 تطبيق التقرير")
        self.style_advanced_button(self.inventory_apply_button, 'emerald')
        self.inventory_apply_button.clicked.connect(self.generate_inventory_report)

        self.inventory_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.inventory_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        inventory_export_menu = QMenu(self)
        inventory_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.inventory_table, "تقرير_المخزون"))
        inventory_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.inventory_table, "تقرير المخزون"))
        inventory_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.inventory_table, "تقرير_المخزون"))
        inventory_export_menu.addAction(csv_action)

        self.inventory_export_button.setMenu(inventory_export_menu)

        self.inventory_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.inventory_refresh_button, 'modern_teal')
        self.inventory_refresh_button.clicked.connect(self.generate_inventory_report)

        buttons_layout.addWidget(self.inventory_apply_button)
        buttons_layout.addWidget(self.inventory_export_button)
        buttons_layout.addWidget(self.inventory_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("📦 نتائج تقرير المخزون")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # إضافة ملخص إحصائي متطور مطابق للتصميم الموحد
        stats_layout = QHBoxLayout()

        # عدد العناصر
        self.inventory_count_label = StyledLabel("عدد العناصر: 0", "info")
        stats_layout.addWidget(self.inventory_count_label.label)

        # إجمالي القيمة
        self.inventory_value_label = StyledLabel("إجمالي القيمة: 0.00 ج.م", "success")
        stats_layout.addWidget(self.inventory_value_label.label)

        # عناصر منخفضة المخزون
        self.inventory_low_stock_label = StyledLabel("عناصر منخفضة المخزون: 0", "warning")
        stats_layout.addWidget(self.inventory_low_stock_label.label)

        results_layout.addLayout(stats_layout)

        # جدول المخزون مطابق لباقي البرنامج
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(8)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "📦 الاسم",
            "🏷️ الفئة",
            "📏 الوحدة",
            "🔢 الكمية",
            "⚠️ الحد الأدنى",
            "💸 سعر التكلفة",
            "💰 سعر البيع",
            "🏢 المورد"
        ]
        self.inventory_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.inventory_table.verticalHeader().setDefaultSectionSize(45)
        self.inventory_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header = self.inventory_table.horizontalHeader()
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.inventory_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.inventory_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.inventory_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def inventory_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.inventory_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.inventory_table, event)

        self.inventory_table.wheelEvent = inventory_wheelEvent

        # عنوان الجدول مع تصميم محسن مطابق للتصميم الموحد
        table_title = QLabel("📦 تفاصيل المخزون")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                padding: 15px 25px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                border: 4px solid #000000;
                border-radius: 12px;
                margin: 8px 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
        """)
        results_layout.addWidget(table_title)
        results_layout.addWidget(self.inventory_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)  # تقليل المسافة بين الفلاتر والنتائج
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية

        self.inventory_tab.setLayout(layout)

    def setup_clients_tab(self):
        """إعداد تبويب تقرير العملاء"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن
        filter_group = QGroupBox("🤝 فلاتر تقرير العملاء")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر العميل مع تصميم محسن
        client_label = QLabel("👤 العميل:")
        client_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.clients_client_combo = QComboBox()
        self.clients_client_combo.addItem("جميع العملاء", None)
        clients = self.session.query(Client).order_by(Client.name).all()
        for client in clients:
            self.clients_client_combo.addItem(client.name, client.id)
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.clients_client_combo, 'primary')
        filter_layout.addRow(client_label, self.clients_client_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.clients_apply_button = QPushButton("🤝 تطبيق التقرير")
        self.style_advanced_button(self.clients_apply_button, 'emerald')
        self.clients_apply_button.clicked.connect(self.generate_clients_report)

        self.clients_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.clients_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        clients_export_menu = QMenu(self)
        clients_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_clients_summary_to_excel())
        clients_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.export_clients_summary_to_pdf())
        clients_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_clients_summary_to_csv())
        clients_export_menu.addAction(csv_action)

        self.clients_export_button.setMenu(clients_export_menu)

        self.clients_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.clients_refresh_button, 'modern_teal')
        self.clients_refresh_button.clicked.connect(self.generate_clients_report)

        buttons_layout.addWidget(self.clients_apply_button)
        buttons_layout.addWidget(self.clients_export_button)
        buttons_layout.addWidget(self.clients_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن
        results_group = QGroupBox("🤝 نتائج تقرير العملاء")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # تقرير العملاء مع تصميم متطور ومحسن
        self.clients_summary = QTextBrowser()
        self.clients_summary.setMinimumHeight(350)
        self.clients_summary.setStyleSheet("""
            QTextBrowser {
                border: 4px solid #000000;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.92),
                    stop:0.6 rgba(226, 232, 240, 0.88),
                    stop:0.8 rgba(203, 213, 225, 0.85),
                    stop:1 rgba(241, 245, 249, 0.95));
                font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                font-size: 17px;
                font-weight: 600;
                color: #0f172a;
                padding: 30px;
                line-height: 1.9;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
                box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.08);
            }
            QScrollBar:vertical {
                border: 3px solid #000000;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                width: 20px;
                border-radius: 10px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:0.3 #2563eb, stop:0.7 #1d4ed8, stop:1 #1e40af);
                border: 2px solid #000000;
                border-radius: 8px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #60a5fa, stop:0.3 #3b82f6, stop:0.7 #2563eb, stop:1 #1d4ed8);
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1d4ed8, stop:0.5 #1e40af, stop:1 #1e3a8a);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # جدول مبيعات العميل مطابق لباقي البرنامج
        self.clients_sales_table = QTableWidget()
        self.clients_sales_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم الفاتورة",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "💳 طريقة الدفع",
            "🎯 الحالة"
        ]
        self.clients_sales_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.clients_sales_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.clients_sales_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                background-clip: padding-box !important;
                box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.2),
                           0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #2563EB,
                    stop:0.3 #3B82F6, stop:0.4 #6366F1, stop:0.5 #8B5CF6,
                    stop:0.6 #A855F7, stop:0.7 #C084FC, stop:0.8 #E879F9,
                    stop:0.9 #F0ABFC, stop:1 #FBBF24) !important;
                color: #FFFFFF !important;
                text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(-2px) !important;
                box-shadow: inset 0 2px 6px rgba(255, 255, 255, 0.3),
                           0 6px 12px rgba(0, 0, 0, 0.4) !important;
            }

            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E293B, stop:0.4 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.8 #6366F1, stop:1 #7C3AED) !important;
                color: #FFFFFF !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(1px) !important;
                box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.4) !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.clients_sales_table.verticalHeader().setDefaultSectionSize(45)
        self.clients_sales_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.clients_sales_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.clients_sales_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.clients_sales_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.clients_sales_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.clients_sales_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.clients_sales_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def clients_sales_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.clients_sales_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.clients_sales_table, event)

        self.clients_sales_table.wheelEvent = clients_sales_wheelEvent

        # إضافة ملخص إحصائي متطور
        stats_layout = QHBoxLayout()

        # عدد العملاء
        self.clients_count_label = StyledLabel("عدد العملاء: 0", "info")
        stats_layout.addWidget(self.clients_count_label.label)

        # إجمالي المبيعات
        self.clients_total_sales_label = StyledLabel("إجمالي المبيعات: 0.00 ج.م", "success")
        stats_layout.addWidget(self.clients_total_sales_label.label)

        # متوسط المبيعات
        self.clients_avg_sales_label = StyledLabel("متوسط المبيعات: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.clients_avg_sales_label.label)

        results_layout.addLayout(stats_layout)
        results_layout.addWidget(self.clients_summary)

        # تم إزالة الجدول والعنوان حسب الطلب

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)  # تقليل المسافة بين الفلاتر والنتائج
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية

        self.clients_tab.setLayout(layout)

    # دوال معالجة الأحداث
    def on_sales_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المبيعات"""
        try:
            period = self.sales_period_combo.currentText()
            if period == "فترة محددة":
                self.sales_start_date.setEnabled(True)
                self.sales_end_date.setEnabled(True)
            else:
                self.sales_start_date.setEnabled(False)
                self.sales_end_date.setEnabled(False)
                self.set_date_range(period, self.sales_start_date, self.sales_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المبيعات: {str(e)}")

    def on_purchases_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المشتريات"""
        try:
            period = self.purchases_period_combo.currentText()
            if period == "فترة محددة":
                self.purchases_start_date.setEnabled(True)
                self.purchases_end_date.setEnabled(True)
            else:
                self.purchases_start_date.setEnabled(False)
                self.purchases_end_date.setEnabled(False)
                self.set_date_range(period, self.purchases_start_date, self.purchases_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المشتريات: {str(e)}")

    def on_profit_loss_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير الأرباح والخسائر"""
        try:
            period = self.profit_loss_period_combo.currentText()
            if period == "فترة محددة":
                self.profit_loss_start_date.setEnabled(True)
                self.profit_loss_end_date.setEnabled(True)
            else:
                self.profit_loss_start_date.setEnabled(False)
                self.profit_loss_end_date.setEnabled(False)
                self.set_date_range(period, self.profit_loss_start_date, self.profit_loss_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير الأرباح والخسائر: {str(e)}")

    def on_expenses_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير المصروفات"""
        try:
            period = self.expenses_period_combo.currentText()
            if period == "فترة محددة":
                self.expenses_start_date.setEnabled(True)
                self.expenses_end_date.setEnabled(True)
            else:
                self.expenses_start_date.setEnabled(False)
                self.expenses_end_date.setEnabled(False)
                self.set_date_range(period, self.expenses_start_date, self.expenses_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير المصروفات: {str(e)}")

    def on_revenues_period_changed(self, index):
        """معالجة تغيير الفترة الزمنية في تقرير الإيرادات"""
        try:
            period = self.revenues_period_combo.currentText()
            if period == "فترة محددة":
                self.revenues_start_date.setEnabled(True)
                self.revenues_end_date.setEnabled(True)
            else:
                self.revenues_start_date.setEnabled(False)
                self.revenues_end_date.setEnabled(False)
                self.set_date_range(period, self.revenues_start_date, self.revenues_end_date)
        except Exception as e:
            print(f"خطأ في معالجة تغيير الفترة الزمنية في تقرير الإيرادات: {str(e)}")

    def set_date_range(self, period, start_date_widget, end_date_widget):
        """تعيين نطاق التاريخ بناءً على الفترة المحددة"""
        today = QDate.currentDate()

        if period == "اليوم":
            start_date_widget.setDate(today)
            end_date_widget.setDate(today)
        elif period == "الأسبوع الحالي":
            start_of_week = today.addDays(-today.dayOfWeek() + 1)
            start_date_widget.setDate(start_of_week)
            end_date_widget.setDate(today)
        elif period == "الشهر الحالي":
            start_of_month = QDate(today.year(), today.month(), 1)
            start_date_widget.setDate(start_of_month)
            end_date_widget.setDate(today)
        elif period == "الربع الحالي":
            quarter = (today.month() - 1) // 3 + 1
            start_month = (quarter - 1) * 3 + 1
            start_of_quarter = QDate(today.year(), start_month, 1)
            start_date_widget.setDate(start_of_quarter)
            end_date_widget.setDate(today)
        elif period == "السنة الحالية":
            start_of_year = QDate(today.year(), 1, 1)
            start_date_widget.setDate(start_of_year)
            end_date_widget.setDate(today)

    # دوال إنتاج التقارير
    def generate_sales_report(self):
        """إنتاج تقرير المبيعات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.sales_start_date.date())
            end_date = qdate_to_datetime(self.sales_end_date.date())
            client_id = self.sales_client_combo.currentData()
            status = self.sales_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Sale).filter(
                Sale.date >= start_date,
                Sale.date <= end_date
            )

            # تطبيق الفلاتر
            if client_id:
                query = query.filter(Sale.client_id == client_id)
            if status:
                query = query.filter(Sale.status == status)

            # تنفيذ الاستعلام
            sales = query.order_by(Sale.date.desc()).all()

            # حساب الإحصائيات
            total_sales = sum(sale.total_amount for sale in sales)
            total_profit = 0

            # حساب الأرباح من عناصر المبيعات
            for sale in sales:
                sale_items = self.session.query(SaleItem).filter(SaleItem.sale_id == sale.id).all()
                for item in sale_items:
                    inventory_item = self.session.query(Inventory).get(item.inventory_id)
                    if inventory_item:
                        profit_per_unit = item.unit_price - inventory_item.cost_price
                        total_profit += profit_per_unit * item.quantity

            # تحديث الملخص
            self.sales_count_label.label.setText(f"عدد الفواتير: {len(sales)}")
            self.sales_total_label.label.setText(f"إجمالي المبيعات: {format_currency(total_sales)}")
            self.sales_profit_label.label.setText(f"إجمالي الأرباح: {format_currency(total_profit)}")

            # تحديث الجدول
            self.sales_table.setRowCount(len(sales))
            for row, sale in enumerate(sales):
                # رقم الفاتورة
                self.sales_table.setItem(row, 0, QTableWidgetItem(sale.sale_number or ""))

                # العميل
                client_name = ""
                if sale.client:
                    client_name = sale.client.name
                self.sales_table.setItem(row, 1, QTableWidgetItem(client_name))

                # التاريخ
                date_str = sale.date.strftime("%Y-%m-%d") if sale.date else ""
                self.sales_table.setItem(row, 2, QTableWidgetItem(date_str))

                # المبلغ الإجمالي
                self.sales_table.setItem(row, 3, QTableWidgetItem(format_currency(sale.total_amount)))

                # المبلغ المدفوع
                self.sales_table.setItem(row, 4, QTableWidgetItem(format_currency(sale.paid_amount)))

                # طريقة الدفع
                payment_methods = {
                    'cash': 'نقدي',
                    'credit': 'آجل',
                    'bank_transfer': 'تحويل بنكي',
                    'check': 'شيك'
                }
                payment_method = payment_methods.get(sale.payment_method, sale.payment_method or "")
                self.sales_table.setItem(row, 5, QTableWidgetItem(payment_method))

                # الحالة
                statuses = {
                    'pending': 'في الانتظار',
                    'completed': 'مكتملة',
                    'cancelled': 'ملغية',
                    'returned': 'مرتجعة'
                }
                status_text = statuses.get(sale.status, sale.status or "")
                self.sales_table.setItem(row, 6, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المبيعات: {str(e)}")

    def generate_purchases_report(self):
        """إنتاج تقرير المشتريات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.purchases_start_date.date())
            end_date = qdate_to_datetime(self.purchases_end_date.date())
            supplier_id = self.purchases_supplier_combo.currentData()
            status = self.purchases_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Purchase).filter(
                Purchase.date >= start_date,
                Purchase.date <= end_date
            )

            # تطبيق الفلاتر
            if supplier_id:
                query = query.filter(Purchase.supplier_id == supplier_id)
            if status:
                query = query.filter(Purchase.status == status)

            # تنفيذ الاستعلام
            purchases = query.order_by(Purchase.date.desc()).all()

            # حساب الإحصائيات
            total_purchases = sum(purchase.total_amount for purchase in purchases)
            total_paid = sum(purchase.paid_amount for purchase in purchases)

            # تحديث الملخص
            self.purchases_count_label.label.setText(f"عدد أوامر الشراء: {len(purchases)}")
            self.purchases_total_label.label.setText(f"إجمالي المشتريات: {format_currency(total_purchases)}")
            self.purchases_paid_label.label.setText(f"إجمالي المدفوعات: {format_currency(total_paid)}")

            # تحديث الجدول
            self.purchases_table.setRowCount(len(purchases))
            for row, purchase in enumerate(purchases):
                # رقم أمر الشراء
                self.purchases_table.setItem(row, 0, QTableWidgetItem(purchase.purchase_number or ""))

                # المورد
                supplier_name = ""
                if purchase.supplier:
                    supplier_name = purchase.supplier.name
                self.purchases_table.setItem(row, 1, QTableWidgetItem(supplier_name))

                # التاريخ
                date_str = purchase.date.strftime("%Y-%m-%d") if purchase.date else ""
                self.purchases_table.setItem(row, 2, QTableWidgetItem(date_str))

                # المبلغ الإجمالي
                self.purchases_table.setItem(row, 3, QTableWidgetItem(format_currency(purchase.total_amount)))

                # المبلغ المدفوع
                self.purchases_table.setItem(row, 4, QTableWidgetItem(format_currency(purchase.paid_amount)))

                # الحالة
                statuses = {
                    'pending': 'في الانتظار',
                    'partially_received': 'مستلم جزئياً',
                    'completed': 'مستلم بالكامل',
                    'cancelled': 'ملغي'
                }
                status_text = statuses.get(purchase.status, purchase.status or "")
                self.purchases_table.setItem(row, 5, QTableWidgetItem(status_text))

            # تحديث الإحصائيات السريعة في الجانب الأيسر
            self.update_quick_purchases_stats(purchases, total_purchases, total_paid)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المشتريات: {str(e)}")

    def set_purchases_period(self, period):
        """تعيين فترة زمنية محددة للمشتريات"""
        try:
            # العثور على الفهرس المطابق في القائمة المنسدلة
            index = self.purchases_period_combo.findText(period)
            if index >= 0:
                self.purchases_period_combo.setCurrentIndex(index)
                # تطبيق التقرير تلقائياً
                self.generate_purchases_report()
        except Exception as e:
            print(f"خطأ في تعيين فترة المشتريات: {str(e)}")

    def update_quick_purchases_stats(self, purchases, total_purchases, total_paid):
        """تحديث الإحصائيات السريعة في الجانب الأيسر"""
        try:
            # تحديث عدد الأوامر
            self.quick_purchases_count.setText(f"📦 عدد الأوامر: {len(purchases)}")

            # تحديث الإجمالي
            self.quick_purchases_total.setText(f"💰 الإجمالي: {format_currency(total_purchases)}")

            # تحديث المدفوع
            self.quick_purchases_paid.setText(f"✅ المدفوع: {format_currency(total_paid)}")

            # حساب وتحديث المتبقي
            remaining = total_purchases - total_paid
            self.quick_purchases_pending.setText(f"⏳ المتبقي: {format_currency(remaining)}")

            # تغيير لون المتبقي حسب القيمة
            if remaining > 0:
                color = "rgba(239, 68, 68, 0.2)"  # أحمر للمتبقي
                border_color = "rgba(239, 68, 68, 0.4)"
            elif remaining < 0:
                color = "rgba(168, 85, 247, 0.2)"  # بنفسجي للزائد
                border_color = "rgba(168, 85, 247, 0.4)"
            else:
                color = "rgba(34, 197, 94, 0.2)"  # أخضر للمكتمل
                border_color = "rgba(34, 197, 94, 0.4)"

            self.quick_purchases_pending.setStyleSheet(f"""
                QLabel {{
                    color: #ffffff;
                    font-size: 11px;
                    font-weight: bold;
                    padding: 4px 8px;
                    background: {color};
                    border: 1px solid {border_color};
                    border-radius: 4px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
                }}
            """)

        except Exception as e:
            print(f"خطأ في تحديث الإحصائيات السريعة: {str(e)}")

    def generate_profit_loss_report(self):
        """إنتاج تقرير الأرباح والخسائر"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.profit_loss_start_date.date())
            end_date = qdate_to_datetime(self.profit_loss_end_date.date())

            # حساب إجمالي المبيعات والأرباح
            sales = self.session.query(Sale).filter(
                Sale.date >= start_date,
                Sale.date <= end_date,
                Sale.status == 'completed'
            ).all()

            total_sales_revenue = sum(sale.total_amount for sale in sales)
            total_sales_cost = 0
            total_sales_profit = 0

            # حساب تكلفة المبيعات والأرباح
            for sale in sales:
                sale_items = self.session.query(SaleItem).filter(SaleItem.sale_id == sale.id).all()
                for item in sale_items:
                    inventory_item = self.session.query(Inventory).get(item.inventory_id)
                    if inventory_item:
                        item_cost = inventory_item.cost_price * item.quantity
                        total_sales_cost += item_cost
                        total_sales_profit += (item.unit_price * item.quantity) - item_cost

            # حساب الإيرادات الأخرى
            revenues = self.session.query(Revenue).filter(
                Revenue.date >= start_date,
                Revenue.date <= end_date
            ).all()
            total_other_revenue = sum(revenue.amount for revenue in revenues)

            # حساب المصروفات
            expenses = self.session.query(Expense).filter(
                Expense.date >= start_date,
                Expense.date <= end_date
            ).all()
            total_expenses = sum(expense.amount for expense in expenses)

            # حساب إجمالي المشتريات
            purchases = self.session.query(Purchase).filter(
                Purchase.date >= start_date,
                Purchase.date <= end_date
            ).all()
            total_purchases = sum(purchase.total_amount for purchase in purchases)

            # حساب الإجماليات
            total_revenue = total_sales_revenue + total_other_revenue
            total_all_expenses = total_expenses + total_sales_cost
            net_profit_loss = total_revenue - total_all_expenses

            # تحديث الملخص السريع
            self.profit_loss_revenue_label.label.setText(f"إجمالي الإيرادات: {format_currency(total_revenue)}")
            self.profit_loss_expenses_label.label.setText(f"إجمالي المصروفات: {format_currency(total_all_expenses)}")
            self.profit_loss_net_label.label.setText(f"صافي الربح/الخسارة: {format_currency(net_profit_loss)}")

            # إنشاء تقرير HTML متطور ومحسن
            html_report = f"""
            <html>
            <head>
                <style>
                    body {{
                        font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                        direction: rtl;
                        background: linear-gradient(135deg,
                            #ffffff 0%, #f8fafc 15%, #f1f5f9 30%,
                            #e2e8f0 45%, #cbd5e1 60%, #94a3b8 75%,
                            #64748b 90%, #475569 100%);
                        margin: 0;
                        padding: 25px;
                        min-height: 100vh;
                        color: #0f172a;
                        line-height: 1.8;
                    }}
                    .header {{
                        text-align: center;
                        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
                        padding: 35px;
                        border-radius: 25px;
                        margin-bottom: 30px;
                        box-shadow: 0 12px 35px rgba(15, 23, 42, 0.15),
                                   0 4px 15px rgba(16, 185, 129, 0.1);
                        border: 4px solid #10b981;
                        position: relative;
                        overflow: hidden;
                    }}
                    .header::before {{
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 4px;
                        background: linear-gradient(90deg, #10b981, #3b82f6, #8b5cf6, #ec4899, #f59e0b);
                    }}
                    .section {{
                        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                        margin: 20px 0;
                        padding: 25px;
                        border-radius: 18px;
                        border-left: 6px solid #10b981;
                        border-right: 2px solid #e2e8f0;
                        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.12),
                                   0 2px 8px rgba(15, 23, 42, 0.08);
                        transition: transform 0.2s ease;
                    }}
                    .section:hover {{
                        transform: translateY(-2px);
                        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.18);
                    }}
                    .section h3 {{
                        color: #10b981;
                        font-size: 20px;
                        margin-top: 0;
                        margin-bottom: 15px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }}
                    .section p {{
                        margin: 12px 0;
                        font-size: 16px;
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 8px 0;
                        border-bottom: 1px solid #f1f5f9;
                    }}
                    .section p:last-child {{
                        border-bottom: none;
                    }}
                    .positive {{
                        color: #059669;
                        font-weight: 700;
                        font-size: 17px;
                        text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2);
                    }}
                    .negative {{
                        color: #dc2626;
                        font-weight: 700;
                        font-size: 17px;
                        text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2);
                    }}
                    .neutral {{
                        color: #10b981;
                        font-weight: 700;
                        font-size: 17px;
                        text-shadow: 0 1px 2px rgba(16, 185, 129, 0.2);
                    }}
                    .total {{
                        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                        color: white;
                        font-size: 20px;
                        padding: 30px;
                        border-radius: 20px;
                        margin: 30px 0;
                        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.25);
                        border: none;
                    }}
                    .total h3 {{
                        color: white;
                        font-size: 24px;
                        margin-bottom: 20px;
                    }}
                    .total p {{
                        font-size: 18px;
                        font-weight: 700;
                    }}
                    .total .positive, .total .negative {{
                        color: white;
                        font-size: 20px;
                        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 style="color: #10b981; font-size: 30px; margin: 0 0 15px 0; text-shadow: 0 2px 4px rgba(16, 185, 129, 0.3);">
                        📊 تقرير الأرباح والخسائر المتطور والشامل
                    </h1>
                    <p style="color: #64748b; font-size: 18px; margin: 10px 0; font-weight: 500;">
                        📈 تحليل مالي تفصيلي للأداء المالي والربحية
                    </p>
                    <div style="margin-top: 20px; padding: 12px; background: rgba(16, 185, 129, 0.1); border-radius: 12px;">
                        <span style="color: #10b981; font-weight: 600; font-size: 16px;">📅 فترة التقرير: </span>
                        <span style="color: #1e293b; font-weight: 500; font-size: 16px;">من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}</span>
                    </div>
                </div>

                <div class="section">
                    <h3>💰 الإيرادات والمبيعات</h3>
                    <p>
                        <span><strong>💵 إجمالي المبيعات:</strong></span>
                        <span class="positive">{format_currency(total_sales_revenue)}</span>
                    </p>
                    <p>
                        <span><strong>📋 عدد فواتير المبيعات:</strong></span>
                        <span class="neutral">{len(sales)}</span>
                    </p>
                    <p>
                        <span><strong>📊 متوسط قيمة الفاتورة:</strong></span>
                        <span class="neutral">{format_currency(total_sales_revenue / len(sales) if sales else 0)}</span>
                    </p>
                </div>

                <div class="section">
                    <h3>💸 التكاليف والمصروفات</h3>
                    <p>
                        <span><strong>🏷️ تكلفة البضاعة المباعة:</strong></span>
                        <span class="negative">{format_currency(total_sales_cost)}</span>
                    </p>
                    <p>
                        <span><strong>🛒 إجمالي المشتريات:</strong></span>
                        <span class="negative">{format_currency(total_purchases)}</span>
                    </p>
                    <p>
                        <span><strong>📦 عدد أوامر الشراء:</strong></span>
                        <span class="neutral">{len(purchases)}</span>
                    </p>
                    <p>
                        <span><strong>📊 متوسط قيمة الطلب:</strong></span>
                        <span class="neutral">{format_currency(total_purchases / len(purchases) if purchases else 0)}</span>
                    </p>
                </div>

                <div class="section">
                    <h3>📈 تحليل الأرباح</h3>
                    <p>
                        <span><strong>💎 إجمالي ربح المبيعات:</strong></span>
                        <span class="positive">{format_currency(total_sales_profit)}</span>
                    </p>
                    <p>
                        <span><strong>📊 هامش الربح:</strong></span>
                        <span class="neutral">{(total_sales_profit / total_sales_revenue * 100) if total_sales_revenue > 0 else 0:.2f}%</span>
                    </p>
                    <p>
                        <span><strong>⚖️ نسبة التكلفة للإيراد:</strong></span>
                        <span class="neutral">{(total_sales_cost / total_sales_revenue * 100) if total_sales_revenue > 0 else 0:.2f}%</span>
                    </p>
                </div>

                <div class="section total">
                    <h3>🎯 النتيجة النهائية</h3>
                    <p>
                        <span><strong>💰 صافي الربح/الخسارة:</strong></span>
                        <span class="{'positive' if net_profit_loss >= 0 else 'negative'}">{format_currency(net_profit_loss)}</span>
                    </p>
                    <p>
                        <span><strong>📊 حالة الأداء:</strong></span>
                        <span class="{'positive' if net_profit_loss >= 0 else 'negative'}">
                            {'أداء ممتاز - ربح صافي' if net_profit_loss > total_sales_revenue * 0.2 else 'أداء جيد - ربح معقول' if net_profit_loss > 0 else 'يحتاج تحسين - خسارة'}
                        </span>
                    </p>
                </div>
            </body>
            </html>
            """

            self.profit_loss_summary.setHtml(html_report)

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الأرباح والخسائر: {str(e)}")

    def generate_inventory_report(self):
        """إنتاج تقرير المخزون"""
        try:
            # الحصول على الفلاتر
            category = self.inventory_category_combo.currentData()
            supplier_id = self.inventory_supplier_combo.currentData()
            status = self.inventory_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Inventory)

            # تطبيق الفلاتر
            if category:
                query = query.filter(Inventory.category == category)
            if supplier_id:
                query = query.filter(Inventory.supplier_id == supplier_id)

            # تطبيق فلتر حالة المخزون
            if status == "low_stock":
                query = query.filter(Inventory.quantity <= Inventory.min_quantity)
            elif status == "out_of_stock":
                query = query.filter(Inventory.quantity == 0)
            elif status == "in_stock":
                query = query.filter(Inventory.quantity > 0)

            # تنفيذ الاستعلام
            inventory_items = query.order_by(Inventory.name).all()

            # حساب الإحصائيات
            total_items = len(inventory_items)
            total_value = sum(item.cost_price * item.quantity for item in inventory_items)
            low_stock_items = len([item for item in inventory_items if item.quantity <= item.min_quantity])

            # تحديث الملخص
            self.inventory_count_label.label.setText(f"عدد العناصر: {total_items}")
            self.inventory_value_label.label.setText(f"قيمة المخزون: {format_currency(total_value)}")
            self.inventory_low_stock_label.label.setText(f"عناصر منخفضة المخزون: {low_stock_items}")

            # تحديث الجدول
            self.inventory_table.setRowCount(len(inventory_items))
            for row, item in enumerate(inventory_items):
                # الاسم
                self.inventory_table.setItem(row, 0, QTableWidgetItem(item.name or ""))

                # الفئة
                self.inventory_table.setItem(row, 1, QTableWidgetItem(item.category or ""))

                # الوحدة
                self.inventory_table.setItem(row, 2, QTableWidgetItem(item.unit or ""))

                # الكمية
                quantity_item = QTableWidgetItem(str(int(item.quantity)))
                if item.quantity <= item.min_quantity:
                    quantity_item.setBackground(QColor(255, 235, 235))  # خلفية حمراء فاتحة
                self.inventory_table.setItem(row, 3, quantity_item)

                # الحد الأدنى
                self.inventory_table.setItem(row, 4, QTableWidgetItem(str(int(item.min_quantity))))

                # سعر التكلفة
                self.inventory_table.setItem(row, 5, QTableWidgetItem(format_currency(item.cost_price)))

                # سعر البيع
                self.inventory_table.setItem(row, 6, QTableWidgetItem(format_currency(item.selling_price)))

                # المورد
                supplier_name = ""
                if item.supplier:
                    supplier_name = item.supplier.name
                self.inventory_table.setItem(row, 7, QTableWidgetItem(supplier_name))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المخزون: {str(e)}")

    def generate_clients_report(self):
        """إنتاج تقرير العملاء المتطور"""
        try:
            # الحصول على الفلاتر
            client_id = self.clients_client_combo.currentData()

            if client_id:
                # تقرير عميل محدد
                client = self.session.query(Client).get(client_id)
                if not client:
                    show_error_message("خطأ", "العميل المحدد غير موجود")
                    return

                # الحصول على مبيعات العميل
                sales = self.session.query(Sale).filter(Sale.client_id == client_id).order_by(Sale.date.desc()).all()

                # حساب الإحصائيات المتقدمة
                total_sales = sum(sale.total_amount for sale in sales)
                total_paid = sum(sale.paid_amount for sale in sales)
                remaining_amount = total_sales - total_paid

                # حساب إحصائيات إضافية
                completed_sales = [s for s in sales if s.status == 'completed']
                pending_sales = [s for s in sales if s.status == 'pending']
                avg_sale_amount = total_sales / len(sales) if sales else 0

                # تحديث الملخص الإحصائي
                self.clients_count_label.label.setText(f"عدد العملاء: 1")
                self.clients_total_sales_label.label.setText(f"إجمالي المبيعات: {format_currency(total_sales)}")
                self.clients_avg_sales_label.label.setText(f"متوسط المبيعات: {format_currency(avg_sale_amount)}")

                # إنشاء تقرير HTML متطور
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{
                            font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                            direction: rtl;
                            background: linear-gradient(135deg,
                                #ffffff 0%, #f8fafc 15%, #f1f5f9 30%,
                                #e2e8f0 45%, #cbd5e1 60%, #94a3b8 75%,
                                #64748b 90%, #475569 100%);
                            margin: 0;
                            padding: 25px;
                            min-height: 100vh;
                            color: #0f172a;
                            line-height: 1.8;
                        }}
                        .header {{
                            text-align: center;
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
                            padding: 35px;
                            border-radius: 25px;
                            margin-bottom: 30px;
                            box-shadow: 0 12px 35px rgba(15, 23, 42, 0.15),
                                       0 4px 15px rgba(59, 130, 246, 0.1);
                            border: 4px solid #3b82f6;
                            position: relative;
                            overflow: hidden;
                        }}
                        .header::before {{
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 4px;
                            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b, #10b981);
                        }}
                        .info-grid {{
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 20px;
                            margin: 25px 0;
                        }}
                        .info-card {{
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                            padding: 20px;
                            border-radius: 18px;
                            border-left: 6px solid #3b82f6;
                            border-right: 2px solid #e2e8f0;
                            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.12),
                                       0 2px 8px rgba(15, 23, 42, 0.08);
                            transition: transform 0.2s ease;
                        }}
                        .info-card:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.18);
                        }}
                        .positive {{ color: #059669; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2); }}
                        .negative {{ color: #dc2626; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2); }}
                        .neutral {{ color: #3b82f6; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(59, 130, 246, 0.2); }}
                        .stats-section {{
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
                            padding: 25px;
                            border-radius: 20px;
                            margin: 25px 0;
                            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15),
                                       0 3px 12px rgba(15, 23, 42, 0.1);
                            border: 2px solid #e2e8f0;
                        }}
                        .stat-item {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 15px 0;
                            border-bottom: 2px solid #f1f5f9;
                            transition: background-color 0.2s ease;
                        }}
                        .stat-item:hover {{
                            background-color: rgba(59, 130, 246, 0.05);
                            border-radius: 8px;
                            margin: 0 -10px;
                            padding: 15px 10px;
                        }}
                        .stat-item:last-child {{
                            border-bottom: none;
                        }}
                        .stat-label {{
                            font-weight: 600;
                            color: #1e293b;
                            font-size: 16px;
                        }}
                        .stat-value {{
                            font-weight: 700;
                            font-size: 16px;
                        }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1 style="color: #3b82f6; font-size: 28px; margin: 0 0 15px 0; text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);">
                            🤝 تقرير العميل المتطور والشامل
                        </h1>
                        <h2 style="color: #1e293b; margin: 10px 0; font-size: 24px; font-weight: 600;">
                            {client.name}
                        </h2>
                        <p style="color: #64748b; font-size: 16px; margin: 10px 0; font-weight: 500;">
                            📊 تقرير تفصيلي وشامل لأداء العميل ومعاملاته المالية
                        </p>
                        <div style="margin-top: 20px; padding: 10px; background: rgba(59, 130, 246, 0.1); border-radius: 10px;">
                            <span style="color: #3b82f6; font-weight: 600;">📅 تاريخ التقرير: </span>
                            <span style="color: #1e293b; font-weight: 500;">{QDate.currentDate().toString('yyyy-MM-dd')}</span>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-card">
                            <h3 style="color: #3b82f6; margin-top: 0; font-size: 20px; display: flex; align-items: center; gap: 10px;">
                                📞 معلومات الاتصال والتواصل
                            </h3>
                            <div style="margin: 15px 0;">
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">📱 الهاتف:</strong>
                                    <span style="color: #059669; font-weight: 600;">{client.phone or 'غير محدد'}</span>
                                </p>
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">🏠 العنوان:</strong>
                                    <span style="color: #059669; font-weight: 600;">{client.address or 'غير محدد'}</span>
                                </p>
                            </div>
                        </div>

                        <div class="info-card">
                            <h3 style="color: #3b82f6; margin-top: 0; font-size: 20px; display: flex; align-items: center; gap: 10px;">
                                📊 إحصائيات سريعة ومؤشرات الأداء
                            </h3>
                            <div style="margin: 15px 0;">
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">📋 عدد الفواتير:</strong>
                                    <span class="neutral">{len(sales)}</span>
                                </p>
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">✅ الفواتير المكتملة:</strong>
                                    <span class="positive">{len(completed_sales)}</span>
                                </p>
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">⏳ الفواتير المعلقة:</strong>
                                    <span class="{'negative' if len(pending_sales) > 0 else 'positive'}">{len(pending_sales)}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="stats-section">
                        <h3 style="color: #3b82f6; margin-top: 0; font-size: 22px; display: flex; align-items: center; gap: 10px; margin-bottom: 20px;">
                            💰 التحليل المالي المتقدم والشامل
                        </h3>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">💵</span>إجمالي المبيعات:
                            </span>
                            <span class="stat-value positive">{format_currency(total_sales)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">💳</span>إجمالي المدفوعات:
                            </span>
                            <span class="stat-value positive">{format_currency(total_paid)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">⚖️</span>المبلغ المتبقي:
                            </span>
                            <span class="stat-value {'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">📊</span>متوسط قيمة الفاتورة:
                            </span>
                            <span class="stat-value neutral">{format_currency(avg_sale_amount)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">📈</span>نسبة السداد:
                            </span>
                            <span class="stat-value {'positive' if total_sales > 0 and (total_paid/total_sales) > 0.8 else 'negative'}">{(total_paid/total_sales*100) if total_sales > 0 else 0:.1f}%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">🎯</span>حالة العميل:
                            </span>
                            <span class="stat-value {'positive' if remaining_amount == 0 else 'negative' if remaining_amount > total_sales * 0.5 else 'neutral'}">
                                {'عميل ممتاز - لا توجد مستحقات' if remaining_amount == 0 else 'عميل جيد - مستحقات قليلة' if remaining_amount <= total_sales * 0.3 else 'يحتاج متابعة - مستحقات عالية'}
                            </span>
                        </div>
                    </div>
                </body>
                </html>
                """

                self.clients_summary.setHtml(html_report)

                # تم إزالة كود تحديث الجدول

            else:
                # تقرير جميع العملاء المتطور
                clients = self.session.query(Client).order_by(Client.name).all()

                # حساب الإحصائيات الإجمالية
                total_clients = len(clients)
                grand_total_sales = 0
                grand_total_paid = 0
                grand_remaining = 0

                clients_data = []
                for client in clients:
                    sales = self.session.query(Sale).filter(Sale.client_id == client.id).all()
                    total_sales = sum(sale.total_amount for sale in sales)
                    total_paid = sum(sale.paid_amount for sale in sales)
                    remaining_amount = total_sales - total_paid

                    grand_total_sales += total_sales
                    grand_total_paid += total_paid
                    grand_remaining += remaining_amount

                    clients_data.append({
                        'name': client.name,
                        'phone': client.phone or 'غير محدد',
                        'invoices_count': len(sales),
                        'total_sales': total_sales,
                        'total_paid': total_paid,
                        'remaining': remaining_amount
                    })

                # تحديث الملخص الإحصائي
                self.clients_count_label.label.setText(f"عدد العملاء: {total_clients}")
                self.clients_total_sales_label.label.setText(f"إجمالي المبيعات: {format_currency(grand_total_sales)}")
                avg_sales = grand_total_sales / total_clients if total_clients > 0 else 0
                self.clients_avg_sales_label.label.setText(f"متوسط المبيعات: {format_currency(avg_sales)}")

                # إنشاء تقرير HTML متطور
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{
                            font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                            direction: rtl;
                            background: linear-gradient(135deg,
                                #ffffff 0%, #f8fafc 15%, #f1f5f9 30%,
                                #e2e8f0 45%, #cbd5e1 60%, #94a3b8 75%,
                                #64748b 90%, #475569 100%);
                            margin: 0;
                            padding: 25px;
                            min-height: 100vh;
                            color: #0f172a;
                            line-height: 1.8;
                        }}
                        .header {{
                            text-align: center;
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
                            padding: 35px;
                            border-radius: 25px;
                            margin-bottom: 30px;
                            box-shadow: 0 12px 35px rgba(15, 23, 42, 0.15),
                                       0 4px 15px rgba(59, 130, 246, 0.1);
                            border: 4px solid #3b82f6;
                            position: relative;
                            overflow: hidden;
                        }}
                        .header::before {{
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 4px;
                            background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899, #f59e0b, #10b981);
                        }}
                        .summary-cards {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                            gap: 20px;
                            margin: 25px 0;
                        }}
                        .summary-card {{
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                            padding: 25px;
                            border-radius: 18px;
                            border-left: 6px solid #3b82f6;
                            border-right: 2px solid #e2e8f0;
                            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.12),
                                       0 2px 8px rgba(15, 23, 42, 0.08);
                            text-align: center;
                            transition: transform 0.2s ease;
                        }}
                        .summary-card:hover {{
                            transform: translateY(-3px);
                            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.18);
                        }}
                        }}
                        .card-title {{
                            color: #1e293b;
                            font-size: 16px;
                            margin-bottom: 12px;
                            font-weight: 600;
                        }}
                        .card-value {{
                            color: #3b82f6;
                            font-size: 26px;
                            font-weight: 700;
                            text-shadow: 0 1px 2px rgba(59, 130, 246, 0.2);
                        }}
                        table {{
                            width: 100%;
                            border-collapse: collapse;
                            margin: 25px 0;
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                            border-radius: 20px;
                            overflow: hidden;
                            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15),
                                       0 3px 12px rgba(15, 23, 42, 0.1);
                            border: 2px solid #e2e8f0;
                        }}
                        th {{
                            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
                            color: white;
                            padding: 18px 12px;
                            font-weight: 700;
                            font-size: 16px;
                            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                        }}
                        td {{
                            padding: 15px 12px;
                            text-align: center;
                            border-bottom: 2px solid rgba(59, 130, 246, 0.1);
                            font-size: 15px;
                            font-weight: 500;
                        }}
                        tr:nth-child(even) {{
                            background-color: rgba(59, 130, 246, 0.05);
                        }}
                        tr:hover {{
                            background-color: rgba(59, 130, 246, 0.1);
                            transform: scale(1.01);
                            transition: all 0.2s ease;
                        }}
                        .positive {{ color: #059669; font-weight: 700; text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2); }}
                        .negative {{ color: #dc2626; font-weight: 700; text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2); }}
                        .neutral {{ color: #3b82f6; font-weight: 700; text-shadow: 0 1px 2px rgba(59, 130, 246, 0.2); }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1 style="color: #3b82f6; font-size: 30px; margin: 0 0 15px 0; text-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);">
                            🤝 تقرير جميع العملاء المتطور والشامل
                        </h1>
                        <p style="color: #64748b; font-size: 18px; margin: 10px 0; font-weight: 500;">
                            📊 تقرير تفصيلي وشامل لجميع العملاء ومعاملاتهم المالية
                        </p>
                        <div style="margin-top: 20px; padding: 12px; background: rgba(59, 130, 246, 0.1); border-radius: 12px;">
                            <span style="color: #3b82f6; font-weight: 600; font-size: 16px;">📅 تاريخ التقرير: </span>
                            <span style="color: #1e293b; font-weight: 500; font-size: 16px;">{QDate.currentDate().toString('yyyy-MM-dd')}</span>
                        </div>
                    </div>

                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-title">إجمالي العملاء</div>
                            <div class="card-value">{total_clients}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">إجمالي المبيعات</div>
                            <div class="card-value">{format_currency(grand_total_sales)}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">إجمالي المدفوعات</div>
                            <div class="card-value">{format_currency(grand_total_paid)}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">المبلغ المتبقي</div>
                            <div class="card-value {'negative' if grand_remaining > 0 else 'positive'}">{format_currency(grand_remaining)}</div>
                        </div>
                    </div>

                    <table>
                        <tr>
                            <th>اسم العميل</th>
                            <th>الهاتف</th>
                            <th>عدد الفواتير</th>
                            <th>إجمالي المبيعات</th>
                            <th>إجمالي المدفوعات</th>
                            <th>المبلغ المتبقي</th>
                        </tr>
                """

                for client_data in clients_data:
                    html_report += f"""
                        <tr>
                            <td style="font-weight: 600; color: #2c3e50;">{client_data['name']}</td>
                            <td>{client_data['phone']}</td>
                            <td class="neutral">{client_data['invoices_count']}</td>
                            <td class="positive">{format_currency(client_data['total_sales'])}</td>
                            <td class="positive">{format_currency(client_data['total_paid'])}</td>
                            <td class="{'negative' if client_data['remaining'] > 0 else 'positive'}">{format_currency(client_data['remaining'])}</td>
                        </tr>
                    """

                html_report += """
                    </table>
                </body>
                </html>
                """

                self.clients_summary.setHtml(html_report)

                # تم إزالة كود إخفاء الجدول

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير العملاء: {str(e)}")

    def export_clients_summary_to_excel(self):
        """تصدير ملخص العملاء إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            import csv
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير العملاء",
                f"تقرير_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if filename:
                # الحصول على النص من الملخص
                html_content = self.clients_summary.toHtml()
                # يمكن تحسين هذا لاحقاً لاستخراج البيانات من HTML
                with open(filename, 'w', encoding='utf-8', newline='') as file:
                    file.write("تقرير العملاء\n")
                    file.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    file.write("تم تصدير الملخص بنجاح\n")

                show_info_message("تم التصدير", f"تم تصدير تقرير العملاء إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_clients_summary_to_pdf(self):
        """تصدير ملخص العملاء إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير العملاء",
                f"تقرير_العملاء_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)

                self.clients_summary.print_(printer)
                show_info_message("تم التصدير", f"تم تصدير تقرير العملاء إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_clients_summary_to_csv(self):
        """تصدير ملخص العملاء إلى CSV"""
        self.export_clients_summary_to_excel()  # نفس الوظيفة

    def export_suppliers_summary_to_excel(self):
        """تصدير ملخص الموردين إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الموردين",
                f"تقرير_الموردين_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8', newline='') as file:
                    file.write("تقرير الموردين\n")
                    file.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    file.write("تم تصدير الملخص بنجاح\n")

                show_info_message("تم التصدير", f"تم تصدير تقرير الموردين إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_suppliers_summary_to_pdf(self):
        """تصدير ملخص الموردين إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الموردين",
                f"تقرير_الموردين_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)

                self.suppliers_summary.print_(printer)
                show_info_message("تم التصدير", f"تم تصدير تقرير الموردين إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_suppliers_summary_to_csv(self):
        """تصدير ملخص الموردين إلى CSV"""
        self.export_suppliers_summary_to_excel()

    def export_expenses_summary_to_excel(self):
        """تصدير ملخص المصروفات إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المصروفات",
                f"تقرير_المصروفات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8', newline='') as file:
                    file.write("تقرير المصروفات\n")
                    file.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    file.write("تم تصدير الملخص بنجاح\n")

                show_info_message("تم التصدير", f"تم تصدير تقرير المصروفات إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_expenses_summary_to_pdf(self):
        """تصدير ملخص المصروفات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير المصروفات",
                f"تقرير_المصروفات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)

                self.expenses_summary.print_(printer)
                show_info_message("تم التصدير", f"تم تصدير تقرير المصروفات إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_expenses_summary_to_csv(self):
        """تصدير ملخص المصروفات إلى CSV"""
        self.export_expenses_summary_to_excel()

    def export_revenues_summary_to_excel(self):
        """تصدير ملخص الإيرادات إلى Excel"""
        try:
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإيرادات",
                f"تقرير_الإيرادات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv)"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8', newline='') as file:
                    file.write("تقرير الإيرادات\n")
                    file.write(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    file.write("تم تصدير الملخص بنجاح\n")

                show_info_message("تم التصدير", f"تم تصدير تقرير الإيرادات إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_revenues_summary_to_pdf(self):
        """تصدير ملخص الإيرادات إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter
            from PyQt5.QtWidgets import QFileDialog
            from datetime import datetime

            filename, _ = QFileDialog.getSaveFileName(
                self, "حفظ تقرير الإيرادات",
                f"تقرير_الإيرادات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
                "PDF Files (*.pdf)"
            )

            if filename:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(filename)

                self.revenues_summary.print_(printer)
                show_info_message("تم التصدير", f"تم تصدير تقرير الإيرادات إلى:\n{filename}")

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_revenues_summary_to_csv(self):
        """تصدير ملخص الإيرادات إلى CSV"""
        self.export_revenues_summary_to_excel()

    def setup_suppliers_tab(self):
        """إعداد تبويب تقرير الموردين"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن مطابق للعملاء
        filter_group = QGroupBox("🏭 فلاتر تقرير الموردين")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر المورد مع تصميم محسن
        supplier_label = QLabel("🏭 المورد:")
        supplier_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.suppliers_supplier_combo = QComboBox()
        self.suppliers_supplier_combo.addItem("جميع الموردين", None)
        suppliers = self.session.query(Supplier).order_by(Supplier.name).all()
        for supplier in suppliers:
            self.suppliers_supplier_combo.addItem(supplier.name, supplier.id)
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.suppliers_supplier_combo, 'primary')
        filter_layout.addRow(supplier_label, self.suppliers_supplier_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.suppliers_apply_button = QPushButton("🏭 تطبيق التقرير")
        self.style_advanced_button(self.suppliers_apply_button, 'emerald')
        self.suppliers_apply_button.clicked.connect(self.generate_suppliers_report)

        self.suppliers_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.suppliers_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        suppliers_export_menu = QMenu(self)
        suppliers_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_suppliers_summary_to_excel())
        suppliers_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.export_suppliers_summary_to_pdf())
        suppliers_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_suppliers_summary_to_csv())
        suppliers_export_menu.addAction(csv_action)

        self.suppliers_export_button.setMenu(suppliers_export_menu)

        self.suppliers_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.suppliers_refresh_button, 'modern_teal')
        self.suppliers_refresh_button.clicked.connect(self.generate_suppliers_report)

        buttons_layout.addWidget(self.suppliers_apply_button)
        buttons_layout.addWidget(self.suppliers_export_button)
        buttons_layout.addWidget(self.suppliers_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن مطابق للعملاء
        results_group = QGroupBox("🏭 نتائج تقرير الموردين")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # تقرير الموردين مع تصميم متطور ومحسن مطابق للعملاء
        self.suppliers_summary = QTextBrowser()
        self.suppliers_summary.setMinimumHeight(350)
        self.suppliers_summary.setStyleSheet("""
            QTextBrowser {
                border: 4px solid #000000;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.92),
                    stop:0.6 rgba(226, 232, 240, 0.88),
                    stop:0.8 rgba(203, 213, 225, 0.85),
                    stop:1 rgba(241, 245, 249, 0.95));
                font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                font-size: 17px;
                font-weight: 600;
                color: #0f172a;
                padding: 30px;
                line-height: 1.9;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
                box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.08);
            }
            QScrollBar:vertical {
                border: 3px solid #000000;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                width: 20px;
                border-radius: 10px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:0.3 #2563eb, stop:0.7 #1d4ed8, stop:1 #1e40af);
                border: 2px solid #000000;
                border-radius: 8px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #60a5fa, stop:0.3 #3b82f6, stop:0.7 #2563eb, stop:1 #1d4ed8);
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1d4ed8, stop:0.5 #1e40af, stop:1 #1e3a8a);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # جدول مشتريات المورد مطابق لباقي البرنامج
        self.suppliers_purchases_table = QTableWidget()
        self.suppliers_purchases_table.setColumnCount(5)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🧾 رقم أمر الشراء",
            "📅 التاريخ",
            "💰 المبلغ الإجمالي",
            "💵 المبلغ المدفوع",
            "🎯 الحالة"
        ]
        self.suppliers_purchases_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.suppliers_purchases_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.suppliers_purchases_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                background-clip: padding-box !important;
                box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.2),
                           0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #2563EB,
                    stop:0.3 #3B82F6, stop:0.4 #6366F1, stop:0.5 #8B5CF6,
                    stop:0.6 #A855F7, stop:0.7 #C084FC, stop:0.8 #E879F9,
                    stop:0.9 #F0ABFC, stop:1 #FBBF24) !important;
                color: #FFFFFF !important;
                text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(-2px) !important;
                box-shadow: inset 0 2px 6px rgba(255, 255, 255, 0.3),
                           0 6px 12px rgba(0, 0, 0, 0.4) !important;
            }

            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E293B, stop:0.4 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.8 #6366F1, stop:1 #7C3AED) !important;
                color: #FFFFFF !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(1px) !important;
                box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.4) !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.suppliers_purchases_table.verticalHeader().setDefaultSectionSize(45)
        self.suppliers_purchases_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.suppliers_purchases_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.suppliers_purchases_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.suppliers_purchases_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.suppliers_purchases_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.suppliers_purchases_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.suppliers_purchases_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def suppliers_purchases_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.suppliers_purchases_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.suppliers_purchases_table, event)

        self.suppliers_purchases_table.wheelEvent = suppliers_purchases_wheelEvent

        # إضافة ملخص إحصائي متطور مطابق للعملاء
        stats_layout = QHBoxLayout()

        # عدد الموردين
        self.suppliers_count_label = StyledLabel("عدد الموردين: 0", "info")
        stats_layout.addWidget(self.suppliers_count_label.label)

        # إجمالي المشتريات
        self.suppliers_total_purchases_label = StyledLabel("إجمالي المشتريات: 0.00 ج.م", "success")
        stats_layout.addWidget(self.suppliers_total_purchases_label.label)

        # متوسط المشتريات
        self.suppliers_avg_purchases_label = StyledLabel("متوسط المشتريات: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.suppliers_avg_purchases_label.label)

        results_layout.addLayout(stats_layout)
        results_layout.addWidget(self.suppliers_summary)

        # تم إزالة الجدول والعنوان حسب الطلب

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)  # تقليل المسافة بين الفلاتر والنتائج
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية

        self.suppliers_tab.setLayout(layout)

    def generate_suppliers_report(self):
        """إنتاج تقرير الموردين"""
        try:
            # الحصول على الفلاتر
            supplier_id = self.suppliers_supplier_combo.currentData()

            if supplier_id:
                # تقرير مورد محدد
                supplier = self.session.query(Supplier).get(supplier_id)
                if not supplier:
                    show_error_message("خطأ", "المورد المحدد غير موجود")
                    return

                # الحصول على مشتريات المورد
                purchases = self.session.query(Purchase).filter(Purchase.supplier_id == supplier_id).order_by(Purchase.date.desc()).all()

                # حساب الإحصائيات
                total_purchases = sum(purchase.total_amount for purchase in purchases)
                total_paid = sum(purchase.paid_amount for purchase in purchases)
                remaining_amount = total_purchases - total_paid

                # إنشاء تقرير HTML متطور ومحسن
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{
                            font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                            direction: rtl;
                            background: linear-gradient(135deg,
                                #ffffff 0%, #f8fafc 15%, #f1f5f9 30%,
                                #e2e8f0 45%, #cbd5e1 60%, #94a3b8 75%,
                                #64748b 90%, #475569 100%);
                            margin: 0;
                            padding: 25px;
                            min-height: 100vh;
                            color: #0f172a;
                            line-height: 1.8;
                        }}
                        .header {{
                            text-align: center;
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
                            padding: 35px;
                            border-radius: 25px;
                            margin-bottom: 30px;
                            box-shadow: 0 12px 35px rgba(15, 23, 42, 0.15),
                                       0 4px 15px rgba(139, 92, 246, 0.1);
                            border: 4px solid #8b5cf6;
                            position: relative;
                            overflow: hidden;
                        }}
                        .header::before {{
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 4px;
                            background: linear-gradient(90deg, #8b5cf6, #3b82f6, #ec4899, #f59e0b, #10b981);
                        }}
                        .info-grid {{
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 20px;
                            margin: 25px 0;
                        }}
                        .info-card {{
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                            padding: 20px;
                            border-radius: 18px;
                            border-left: 6px solid #8b5cf6;
                            border-right: 2px solid #e2e8f0;
                            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.12),
                                       0 2px 8px rgba(15, 23, 42, 0.08);
                            transition: transform 0.2s ease;
                        }}
                        .info-card:hover {{
                            transform: translateY(-2px);
                            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.18);
                        }}
                        .stats-section {{
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
                            padding: 25px;
                            border-radius: 20px;
                            margin: 25px 0;
                            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15),
                                       0 3px 12px rgba(15, 23, 42, 0.1);
                            border: 2px solid #e2e8f0;
                        }}
                        .stat-item {{
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 15px 0;
                            border-bottom: 2px solid #f1f5f9;
                            transition: background-color 0.2s ease;
                        }}
                        .stat-item:hover {{
                            background-color: rgba(139, 92, 246, 0.05);
                            border-radius: 8px;
                            margin: 0 -10px;
                            padding: 15px 10px;
                        }}
                        .stat-item:last-child {{
                            border-bottom: none;
                        }}
                        .stat-label {{
                            font-weight: 600;
                            color: #1e293b;
                            font-size: 16px;
                        }}
                        .stat-value {{
                            font-weight: 700;
                            font-size: 16px;
                        }}
                        .positive {{ color: #059669; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2); }}
                        .negative {{ color: #dc2626; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2); }}
                        .neutral {{ color: #8b5cf6; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(139, 92, 246, 0.2); }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1 style="color: #8b5cf6; font-size: 28px; margin: 0 0 15px 0; text-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);">
                            🏢 تقرير المورد المتطور والشامل
                        </h1>
                        <h2 style="color: #1e293b; margin: 10px 0; font-size: 24px; font-weight: 600;">
                            {supplier.name}
                        </h2>
                        <p style="color: #64748b; font-size: 16px; margin: 10px 0; font-weight: 500;">
                            📊 تقرير تفصيلي وشامل لأداء المورد ومعاملاته المالية
                        </p>
                        <div style="margin-top: 20px; padding: 10px; background: rgba(139, 92, 246, 0.1); border-radius: 10px;">
                            <span style="color: #8b5cf6; font-weight: 600;">📅 تاريخ التقرير: </span>
                            <span style="color: #1e293b; font-weight: 500;">{QDate.currentDate().toString('yyyy-MM-dd')}</span>
                        </div>
                    </div>

                    <div class="info-grid">
                        <div class="info-card">
                            <h3 style="color: #8b5cf6; margin-top: 0; font-size: 20px; display: flex; align-items: center; gap: 10px;">
                                📞 معلومات الاتصال والتواصل
                            </h3>
                            <div style="margin: 15px 0;">
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">📱 الهاتف:</strong>
                                    <span style="color: #059669; font-weight: 600;">{supplier.phone or 'غير محدد'}</span>
                                </p>
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">🏠 العنوان:</strong>
                                    <span style="color: #059669; font-weight: 600;">{supplier.address or 'غير محدد'}</span>
                                </p>
                            </div>
                        </div>

                        <div class="info-card">
                            <h3 style="color: #8b5cf6; margin-top: 0; font-size: 20px; display: flex; align-items: center; gap: 10px;">
                                📊 إحصائيات سريعة ومؤشرات الأداء
                            </h3>
                            <div style="margin: 15px 0;">
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">📋 عدد أوامر الشراء:</strong>
                                    <span class="neutral">{len(purchases)}</span>
                                </p>
                                <p style="margin: 8px 0; font-size: 16px;">
                                    <strong style="color: #1e293b;">🎯 حالة المورد:</strong>
                                    <span class="{'positive' if remaining_amount == 0 else 'negative' if remaining_amount > total_purchases * 0.5 else 'neutral'}">
                                        {'مورد ممتاز - لا توجد مستحقات' if remaining_amount == 0 else 'مورد جيد - مستحقات قليلة' if remaining_amount <= total_purchases * 0.3 else 'يحتاج متابعة - مستحقات عالية'}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="stats-section">
                        <h3 style="color: #8b5cf6; margin-top: 0; font-size: 22px; display: flex; align-items: center; gap: 10px; margin-bottom: 20px;">
                            💰 التحليل المالي المتقدم والشامل
                        </h3>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">🛒</span>إجمالي المشتريات:
                            </span>
                            <span class="stat-value negative">{format_currency(total_purchases)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">💳</span>إجمالي المدفوعات:
                            </span>
                            <span class="stat-value positive">{format_currency(total_paid)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">⚖️</span>المبلغ المتبقي:
                            </span>
                            <span class="stat-value {'negative' if remaining_amount > 0 else 'positive'}">{format_currency(remaining_amount)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">📊</span>متوسط قيمة الطلب:
                            </span>
                            <span class="stat-value neutral">{format_currency(total_purchases / len(purchases) if purchases else 0)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">
                                <span style="margin-left: 8px;">📈</span>نسبة السداد:
                            </span>
                            <span class="stat-value {'positive' if total_purchases > 0 and (total_paid/total_purchases) > 0.8 else 'negative'}">{(total_paid/total_purchases*100) if total_purchases > 0 else 0:.1f}%</span>
                        </div>
                    </div>
                </body>
                </html>
                """

                self.suppliers_summary.setHtml(html_report)

                # تم إزالة كود تحديث الجدول

            else:
                # تقرير جميع الموردين
                suppliers = self.session.query(Supplier).order_by(Supplier.name).all()

                # حساب الإحصائيات الإجمالية
                total_suppliers = len(suppliers)
                grand_total_purchases = 0
                grand_total_paid = 0
                grand_remaining = 0

                suppliers_data = []
                for supplier in suppliers:
                    purchases = self.session.query(Purchase).filter(Purchase.supplier_id == supplier.id).all()
                    total_purchases = sum(purchase.total_amount for purchase in purchases)
                    total_paid = sum(purchase.paid_amount for purchase in purchases)
                    remaining_amount = total_purchases - total_paid

                    grand_total_purchases += total_purchases
                    grand_total_paid += total_paid
                    grand_remaining += remaining_amount

                    suppliers_data.append({
                        'name': supplier.name,
                        'phone': supplier.phone or 'غير محدد',
                        'orders_count': len(purchases),
                        'total_purchases': total_purchases,
                        'total_paid': total_paid,
                        'remaining': remaining_amount
                    })

                # إنشاء تقرير HTML متطور ومحسن
                html_report = f"""
                <html>
                <head>
                    <style>
                        body {{
                            font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                            direction: rtl;
                            background: linear-gradient(135deg,
                                #ffffff 0%, #f8fafc 15%, #f1f5f9 30%,
                                #e2e8f0 45%, #cbd5e1 60%, #94a3b8 75%,
                                #64748b 90%, #475569 100%);
                            margin: 0;
                            padding: 25px;
                            min-height: 100vh;
                            color: #0f172a;
                            line-height: 1.8;
                        }}
                        .header {{
                            text-align: center;
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffffff 100%);
                            padding: 35px;
                            border-radius: 25px;
                            margin-bottom: 30px;
                            box-shadow: 0 12px 35px rgba(15, 23, 42, 0.15),
                                       0 4px 15px rgba(139, 92, 246, 0.1);
                            border: 4px solid #8b5cf6;
                            position: relative;
                            overflow: hidden;
                        }}
                        .header::before {{
                            content: '';
                            position: absolute;
                            top: 0;
                            left: 0;
                            right: 0;
                            height: 4px;
                            background: linear-gradient(90deg, #8b5cf6, #3b82f6, #ec4899, #f59e0b, #10b981);
                        }}
                        .summary-cards {{
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                            gap: 20px;
                            margin: 25px 0;
                        }}
                        .summary-card {{
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                            padding: 25px;
                            border-radius: 18px;
                            border-left: 6px solid #8b5cf6;
                            border-right: 2px solid #e2e8f0;
                            box-shadow: 0 6px 20px rgba(139, 92, 246, 0.12),
                                       0 2px 8px rgba(15, 23, 42, 0.08);
                            text-align: center;
                            transition: transform 0.2s ease;
                        }}
                        .summary-card:hover {{
                            transform: translateY(-3px);
                            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.18);
                        }}
                        .card-title {{
                            color: #1e293b;
                            font-size: 16px;
                            margin-bottom: 12px;
                            font-weight: 600;
                        }}
                        .card-value {{
                            color: #8b5cf6;
                            font-size: 26px;
                            font-weight: 700;
                            text-shadow: 0 1px 2px rgba(139, 92, 246, 0.2);
                        }}
                        table {{
                            width: 100%;
                            border-collapse: collapse;
                            margin: 25px 0;
                            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                            border-radius: 20px;
                            overflow: hidden;
                            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.15),
                                       0 3px 12px rgba(15, 23, 42, 0.1);
                            border: 2px solid #e2e8f0;
                        }}
                        th {{
                            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 50%, #6d28d9 100%);
                            color: white;
                            padding: 18px 12px;
                            font-weight: 700;
                            font-size: 16px;
                            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
                        }}
                        td {{
                            padding: 15px 12px;
                            text-align: center;
                            border-bottom: 2px solid rgba(139, 92, 246, 0.1);
                            font-size: 15px;
                            font-weight: 500;
                        }}
                        tr:nth-child(even) {{
                            background-color: rgba(139, 92, 246, 0.05);
                        }}
                        tr:hover {{
                            background-color: rgba(139, 92, 246, 0.1);
                            transform: scale(1.01);
                            transition: all 0.2s ease;
                        }}
                        .positive {{ color: #059669; font-weight: 700; text-shadow: 0 1px 2px rgba(5, 150, 105, 0.2); }}
                        .negative {{ color: #dc2626; font-weight: 700; text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2); }}
                        .neutral {{ color: #8b5cf6; font-weight: 700; text-shadow: 0 1px 2px rgba(139, 92, 246, 0.2); }}
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1 style="color: #8b5cf6; font-size: 30px; margin: 0 0 15px 0; text-shadow: 0 2px 4px rgba(139, 92, 246, 0.3);">
                            🏢 تقرير جميع الموردين المتطور والشامل
                        </h1>
                        <p style="color: #64748b; font-size: 18px; margin: 10px 0; font-weight: 500;">
                            📊 تقرير تفصيلي وشامل لجميع الموردين ومعاملاتهم المالية
                        </p>
                        <div style="margin-top: 20px; padding: 12px; background: rgba(139, 92, 246, 0.1); border-radius: 12px;">
                            <span style="color: #8b5cf6; font-weight: 600; font-size: 16px;">📅 تاريخ التقرير: </span>
                            <span style="color: #1e293b; font-weight: 500; font-size: 16px;">{QDate.currentDate().toString('yyyy-MM-dd')}</span>
                        </div>
                    </div>

                    <div class="summary-cards">
                        <div class="summary-card">
                            <div class="card-title">إجمالي الموردين</div>
                            <div class="card-value">{total_suppliers}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">إجمالي المشتريات</div>
                            <div class="card-value negative">{format_currency(grand_total_purchases)}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">إجمالي المدفوعات</div>
                            <div class="card-value positive">{format_currency(grand_total_paid)}</div>
                        </div>
                        <div class="summary-card">
                            <div class="card-title">المبلغ المتبقي</div>
                            <div class="card-value {'negative' if grand_remaining > 0 else 'positive'}">{format_currency(grand_remaining)}</div>
                        </div>
                    </div>

                    <table>
                        <tr>
                            <th>اسم المورد</th>
                            <th>الهاتف</th>
                            <th>عدد أوامر الشراء</th>
                            <th>إجمالي المشتريات</th>
                            <th>إجمالي المدفوعات</th>
                            <th>المبلغ المتبقي</th>
                        </tr>
                """

                for supplier_data in suppliers_data:
                    html_report += f"""
                        <tr>
                            <td style="font-weight: 600; color: #2c3e50;">{supplier_data['name']}</td>
                            <td>{supplier_data['phone']}</td>
                            <td class="neutral">{supplier_data['orders_count']}</td>
                            <td class="negative">{format_currency(supplier_data['total_purchases'])}</td>
                            <td class="positive">{format_currency(supplier_data['total_paid'])}</td>
                            <td class="{'negative' if supplier_data['remaining'] > 0 else 'positive'}">{format_currency(supplier_data['remaining'])}</td>
                        </tr>
                    """

                html_report += """
                    </table>
                </body>
                </html>
                """

                self.suppliers_summary.setHtml(html_report)

                # تم إزالة كود إخفاء الجدول

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الموردين: {str(e)}")

    def setup_expenses_tab(self):
        """إعداد تبويب تقرير المصروفات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن مطابق للتصميم الموحد
        filter_group = QGroupBox("💸 فلاتر تقرير المصروفات")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.expenses_period_combo = QComboBox()
        self.expenses_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.expenses_period_combo.currentIndexChanged.connect(self.on_expenses_period_changed)
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.expenses_period_combo, 'primary')
        filter_layout.addRow(period_label, self.expenses_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية مع التصميم الموحد
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.expenses_start_date = QDateEdit()
        self.expenses_start_date.setCalendarPopup(True)
        self.expenses_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.expenses_start_date.setEnabled(False)
        # تطبيق التصميم المتطور الموحد
        self.expenses_start_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 35px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.3 #cbd5e1, stop:0.7 #94a3b8, stop:1 #64748b);
                border: 3px solid #3B82F6;
                color: #0f172a;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                color: #64748b;
                border: 3px solid #cbd5e1;
            }
        """)

        # تسمية وحقل تاريخ النهاية مع التصميم الموحد
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.expenses_end_date = QDateEdit()
        self.expenses_end_date.setCalendarPopup(True)
        self.expenses_end_date.setDate(QDate.currentDate())
        self.expenses_end_date.setEnabled(False)
        # تطبيق التصميم المتطور الموحد
        self.expenses_end_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 35px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.3 #cbd5e1, stop:0.7 #94a3b8, stop:1 #64748b);
                border: 3px solid #3B82F6;
                color: #0f172a;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                color: #64748b;
                border: 3px solid #cbd5e1;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.expenses_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.expenses_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # فلتر نوع المصروف مع تصميم محسن
        category_label = QLabel("📂 نوع المصروف:")
        category_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.expenses_category_combo = QComboBox()
        self.expenses_category_combo.addItem("جميع الأنواع", None)
        # سنضيف الفئات من قاعدة البيانات
        try:
            categories = self.session.query(Expense.category).distinct().all()
            for category in categories:
                if category[0]:
                    self.expenses_category_combo.addItem(category[0], category[0])
        except:
            pass
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.expenses_category_combo, 'primary')
        filter_layout.addRow(category_label, self.expenses_category_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.expenses_apply_button = QPushButton("💸 تطبيق التقرير")
        self.style_advanced_button(self.expenses_apply_button, 'emerald')
        self.expenses_apply_button.clicked.connect(self.generate_expenses_report)

        self.expenses_export_button = QPushButton("📤 تصدير")
        self.style_advanced_button(self.expenses_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        expenses_export_menu = QMenu(self)
        expenses_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_expenses_summary_to_excel())
        expenses_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.export_expenses_summary_to_pdf())
        expenses_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_expenses_summary_to_csv())
        expenses_export_menu.addAction(csv_action)

        self.expenses_export_button.setMenu(expenses_export_menu)

        self.expenses_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.expenses_refresh_button, 'modern_teal')
        self.expenses_refresh_button.clicked.connect(self.generate_expenses_report)

        buttons_layout.addWidget(self.expenses_apply_button)
        buttons_layout.addWidget(self.expenses_export_button)
        buttons_layout.addWidget(self.expenses_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن مطابق للتصميم الموحد
        results_group = QGroupBox("💸 نتائج تقرير المصروفات")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # إضافة ملخص إحصائي متطور مطابق للتصميم الموحد
        stats_layout = QHBoxLayout()

        # عدد المصروفات
        self.expenses_count_label = StyledLabel("عدد المصروفات: 0", "info")
        stats_layout.addWidget(self.expenses_count_label.label)

        # إجمالي المصروفات
        self.expenses_total_label = StyledLabel("إجمالي المصروفات: 0.00 ج.م", "success")
        stats_layout.addWidget(self.expenses_total_label.label)

        # متوسط المصروفات
        self.expenses_avg_label = StyledLabel("متوسط المصروفات: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.expenses_avg_label.label)

        results_layout.addLayout(stats_layout)

        # تقرير المصروفات مع تصميم متطور ومحسن مطابق للتصميم الموحد
        self.expenses_summary = QTextBrowser()
        self.expenses_summary.setMinimumHeight(350)
        self.expenses_summary.setStyleSheet("""
            QTextBrowser {
                border: 4px solid #000000;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.92),
                    stop:0.6 rgba(226, 232, 240, 0.88),
                    stop:0.8 rgba(203, 213, 225, 0.85),
                    stop:1 rgba(241, 245, 249, 0.95));
                font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                font-size: 17px;
                font-weight: 600;
                color: #0f172a;
                padding: 30px;
                line-height: 1.9;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
                box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.08);
            }
            QScrollBar:vertical {
                border: 3px solid #000000;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                width: 20px;
                border-radius: 10px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:0.3 #2563eb, stop:0.7 #1d4ed8, stop:1 #1e40af);
                border: 2px solid #000000;
                border-radius: 8px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #60a5fa, stop:0.3 #3b82f6, stop:0.7 #2563eb, stop:1 #1d4ed8);
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1d4ed8, stop:0.5 #1e40af, stop:1 #1e3a8a);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # جدول المصروفات مع تصميم متطور
        self.expenses_table = QTableWidget()
        self.expenses_table.setColumnCount(5)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "📅 التاريخ",
            "📝 الوصف",
            "🏷️ النوع",
            "💸 المبلغ",
            "📋 الملاحظات"
        ]
        self.expenses_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.expenses_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.expenses_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                background-clip: padding-box !important;
                box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.2),
                           0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #2563EB,
                    stop:0.3 #3B82F6, stop:0.4 #6366F1, stop:0.5 #8B5CF6,
                    stop:0.6 #A855F7, stop:0.7 #C084FC, stop:0.8 #E879F9,
                    stop:0.9 #F0ABFC, stop:1 #FBBF24) !important;
                color: #FFFFFF !important;
                text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(-2px) !important;
                box-shadow: inset 0 2px 6px rgba(255, 255, 255, 0.3),
                           0 6px 12px rgba(0, 0, 0, 0.4) !important;
            }

            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E293B, stop:0.4 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.8 #6366F1, stop:1 #7C3AED) !important;
                color: #FFFFFF !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(1px) !important;
                box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.4) !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.expenses_table.verticalHeader().setDefaultSectionSize(45)
        self.expenses_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.expenses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.expenses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.expenses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.expenses_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.expenses_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.expenses_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def expenses_reports_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.expenses_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.expenses_table, event)

        self.expenses_table.wheelEvent = expenses_reports_wheelEvent

        results_layout.addWidget(self.expenses_summary)

        # تم إزالة الجدول والعنوان حسب الطلب

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)  # تقليل المسافة بين الفلاتر والنتائج
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية

        self.expenses_tab.setLayout(layout)

    def generate_expenses_report(self):
        """إنتاج تقرير المصروفات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.expenses_start_date.date())
            end_date = qdate_to_datetime(self.expenses_end_date.date())
            category = self.expenses_category_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Expense).filter(
                Expense.date >= start_date,
                Expense.date <= end_date
            )

            # تطبيق فلتر النوع
            if category:
                query = query.filter(Expense.category == category)

            # تنفيذ الاستعلام
            expenses = query.order_by(Expense.date.desc()).all()

            # حساب الإحصائيات المتقدمة
            total_expenses = sum(expense.amount for expense in expenses)

            # تجميع المصروفات حسب الفئة
            categories_stats = {}
            for expense in expenses:
                category = expense.category or 'غير محدد'
                if category not in categories_stats:
                    categories_stats[category] = {'count': 0, 'total': 0}
                categories_stats[category]['count'] += 1
                categories_stats[category]['total'] += expense.amount

            # حساب متوسط المصروف
            avg_expense = total_expenses / len(expenses) if expenses else 0

            # أكبر وأصغر مصروف
            max_expense = max(expenses, key=lambda x: x.amount) if expenses else None
            min_expense = min(expenses, key=lambda x: x.amount) if expenses else None

            # تحديث الملخص
            self.expenses_count_label.setText(f"💸 عدد المصروفات: {len(expenses)}")
            self.expenses_total_label.setText(f"💰 إجمالي المصروفات: {format_currency(total_expenses)}")

            # إنشاء تقرير HTML متطور ومحسن
            html_report = f"""
            <html>
            <head>
                <style>
                    body {{
                        font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                        direction: rtl;
                        background: linear-gradient(135deg,
                            #ffffff 0%, #fef2f2 15%, #fee2e2 30%,
                            #fecaca 45%, #fca5a5 60%, #f87171 75%,
                            #ef4444 90%, #dc2626 100%);
                        margin: 0;
                        padding: 25px;
                        min-height: 100vh;
                        color: #0f172a;
                        line-height: 1.8;
                    }}
                    .header {{
                        text-align: center;
                        background: linear-gradient(135deg, #ffffff 0%, #fef2f2 50%, #ffffff 100%);
                        padding: 35px;
                        border-radius: 25px;
                        margin-bottom: 30px;
                        box-shadow: 0 12px 35px rgba(15, 23, 42, 0.15),
                                   0 4px 15px rgba(220, 38, 38, 0.1);
                        border: 4px solid #dc2626;
                        position: relative;
                        overflow: hidden;
                    }}
                    .header::before {{
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 4px;
                        background: linear-gradient(90deg, #dc2626, #ef4444, #f87171, #fca5a5, #fee2e2);
                    }}
                    .summary-cards {{
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                        gap: 20px;
                        margin: 25px 0;
                    }}
                    .summary-card {{
                        background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
                        padding: 25px;
                        border-radius: 18px;
                        border-left: 6px solid #dc2626;
                        border-right: 2px solid #fee2e2;
                        box-shadow: 0 6px 20px rgba(220, 38, 38, 0.12),
                                   0 2px 8px rgba(15, 23, 42, 0.08);
                        text-align: center;
                        transition: transform 0.2s ease;
                    }}
                    .summary-card:hover {{
                        transform: translateY(-3px);
                        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.18);
                    }}
                    .card-title {{
                        color: #1e293b;
                        font-size: 16px;
                        margin-bottom: 12px;
                        font-weight: 600;
                    }}
                    .card-value {{
                        color: #dc2626;
                        font-size: 26px;
                        font-weight: 700;
                        text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2);
                    }}
                    .stats-section {{
                        background: linear-gradient(135deg, #ffffff 0%, #fef2f2 50%, #ffffff 100%);
                        padding: 25px;
                        border-radius: 20px;
                        margin: 25px 0;
                        box-shadow: 0 8px 25px rgba(220, 38, 38, 0.15),
                                   0 3px 12px rgba(15, 23, 42, 0.1);
                        border: 2px solid #fee2e2;
                    }}
                    .stats-section h3 {{
                        color: #dc2626;
                        font-size: 22px;
                        margin-top: 0;
                        margin-bottom: 20px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }}
                    .stat-item {{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 15px 0;
                        border-bottom: 2px solid #fef2f2;
                        transition: background-color 0.2s ease;
                    }}
                    .stat-item:hover {{
                        background-color: rgba(220, 38, 38, 0.05);
                        border-radius: 8px;
                        margin: 0 -10px;
                        padding: 15px 10px;
                    }}
                    .stat-item:last-child {{
                        border-bottom: none;
                    }}
                    .stat-label {{
                        font-weight: 600;
                        color: #1e293b;
                        font-size: 16px;
                    }}
                    .stat-value {{
                        font-weight: 700;
                        font-size: 16px;
                    }}
                    .negative {{ color: #dc2626; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2); }}
                    .neutral {{ color: #dc2626; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(220, 38, 38, 0.2); }}
                    .category-item {{
                        background: rgba(220, 38, 38, 0.05);
                        padding: 12px;
                        margin: 8px 0;
                        border-radius: 10px;
                        border-left: 4px solid #dc2626;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 style="color: #dc2626; font-size: 30px; margin: 0 0 15px 0; text-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);">
                        💸 تقرير المصروفات المتطور والشامل
                    </h1>
                    <p style="color: #64748b; font-size: 18px; margin: 10px 0; font-weight: 500;">
                        📊 تحليل تفصيلي وشامل للمصروفات والنفقات
                    </p>
                    <div style="margin-top: 20px; padding: 12px; background: rgba(220, 38, 38, 0.1); border-radius: 12px;">
                        <span style="color: #dc2626; font-weight: 600; font-size: 16px;">📅 فترة التقرير: </span>
                        <span style="color: #1e293b; font-weight: 500; font-size: 16px;">من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}</span>
                    </div>
                </div>

                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-title">إجمالي المصروفات</div>
                        <div class="card-value">{len(expenses)}</div>
                    </div>
                    <div class="summary-card">
                        <div class="card-title">إجمالي المبلغ</div>
                        <div class="card-value negative">{format_currency(total_expenses)}</div>
                    </div>
                    <div class="summary-card">
                        <div class="card-title">متوسط المصروف</div>
                        <div class="card-value neutral">{format_currency(avg_expense)}</div>
                    </div>
                    <div class="summary-card">
                        <div class="card-title">عدد الفئات</div>
                        <div class="card-value">{len(categories_stats)}</div>
                    </div>
                </div>

                <div class="stats-section">
                    <h3>📊 إحصائيات تفصيلية</h3>
                    <div class="stat-item">
                        <span class="stat-label">
                            <span style="margin-left: 8px;">🔺</span>أكبر مصروف:
                        </span>
                        <span class="stat-value negative">{format_currency(max_expense.amount) if max_expense else '0'} ({max_expense.description if max_expense else 'لا يوجد'})</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">
                            <span style="margin-left: 8px;">🔻</span>أصغر مصروف:
                        </span>
                        <span class="stat-value negative">{format_currency(min_expense.amount) if min_expense else '0'} ({min_expense.description if min_expense else 'لا يوجد'})</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">
                            <span style="margin-left: 8px;">📈</span>متوسط المصروف اليومي:
                        </span>
                        <span class="stat-value neutral">{format_currency(total_expenses / ((end_date - start_date).days + 1)) if expenses else 0}</span>
                    </div>
                </div>

                <div class="stats-section">
                    <h3>🏷️ تحليل المصروفات حسب الفئة</h3>
            """

            for category, stats in categories_stats.items():
                percentage = (stats['total'] / total_expenses * 100) if total_expenses > 0 else 0
                html_report += f"""
                    <div class="category-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600; color: #1e293b;">{category}</span>
                            <div style="text-align: left;">
                                <span style="color: #dc2626; font-weight: 700;">{format_currency(stats['total'])}</span>
                                <span style="color: #64748b; font-size: 14px; margin-right: 10px;">({stats['count']} مصروف - {percentage:.1f}%)</span>
                            </div>
                        </div>
                    </div>
                """

            html_report += """
                </div>
            </body>
            </html>
            """

            self.expenses_summary.setHtml(html_report)

            # تم إزالة كود تحديث الجدول

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المصروفات: {str(e)}")

    def setup_revenues_tab(self):
        """إعداد تبويب تقرير الإيرادات"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن مطابق للتصميم الموحد
        filter_group = QGroupBox("💰 فلاتر تقرير الإيرادات")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر الفترة الزمنية مع تصميم محسن مطابق للتصميم الموحد
        period_label = QLabel("📅 الفترة الزمنية:")
        period_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.revenues_period_combo = QComboBox()
        self.revenues_period_combo.addItems(["اليوم", "الأسبوع الحالي", "الشهر الحالي", "الربع الحالي", "السنة الحالية", "فترة محددة"])
        self.revenues_period_combo.currentIndexChanged.connect(self.on_revenues_period_changed)
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.revenues_period_combo, 'primary')
        filter_layout.addRow(period_label, self.revenues_period_combo)

        # حقول تاريخ البداية والنهاية مع تصميم محسن
        date_layout = QHBoxLayout()
        date_layout.setSpacing(15)

        # تسمية وحقل تاريخ البداية مع التصميم الموحد
        start_label = QLabel("📅 من:")
        start_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.revenues_start_date = QDateEdit()
        self.revenues_start_date.setCalendarPopup(True)
        self.revenues_start_date.setDate(QDate.currentDate().addMonths(-1))
        self.revenues_start_date.setEnabled(False)
        # تطبيق التصميم المتطور الموحد
        self.revenues_start_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 35px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.3 #cbd5e1, stop:0.7 #94a3b8, stop:1 #64748b);
                border: 3px solid #3B82F6;
                color: #0f172a;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                color: #64748b;
                border: 3px solid #cbd5e1;
            }
        """)

        # تسمية وحقل تاريخ النهاية مع التصميم الموحد
        end_label = QLabel("📅 إلى:")
        end_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.revenues_end_date = QDateEdit()
        self.revenues_end_date.setCalendarPopup(True)
        self.revenues_end_date.setDate(QDate.currentDate())
        self.revenues_end_date.setEnabled(False)
        # تطبيق التصميم المتطور الموحد
        self.revenues_end_date.setStyleSheet("""
            QDateEdit {
                border: 3px solid #000000;
                border-radius: 12px;
                padding: 12px 16px;
                font-size: 15px;
                font-weight: bold;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                color: #1e293b;
                min-height: 35px;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }
            QDateEdit:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #e2e8f0, stop:0.3 #cbd5e1, stop:0.7 #94a3b8, stop:1 #64748b);
                border: 3px solid #3B82F6;
                color: #0f172a;
            }
            QDateEdit:disabled {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.5 #e2e8f0, stop:1 #cbd5e1);
                color: #64748b;
                border: 3px solid #cbd5e1;
            }
        """)

        date_layout.addWidget(start_label)
        date_layout.addWidget(self.revenues_start_date)
        date_layout.addWidget(end_label)
        date_layout.addWidget(self.revenues_end_date)
        date_layout.addStretch()

        filter_layout.addRow("", date_layout)

        # فلتر نوع الإيراد مع تصميم محسن
        category_label = QLabel("📂 نوع الإيراد:")
        category_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.revenues_category_combo = QComboBox()
        self.revenues_category_combo.addItem("جميع الأنواع", None)
        # سنضيف الفئات من قاعدة البيانات
        try:
            categories = self.session.query(Revenue.category).distinct().all()
            for category in categories:
                if category[0]:
                    self.revenues_category_combo.addItem(category[0], category[0])
        except:
            pass
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.revenues_category_combo, 'primary')
        filter_layout.addRow(category_label, self.revenues_category_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.revenues_apply_button = QPushButton("💰 تطبيق التقرير")
        self.style_advanced_button(self.revenues_apply_button, 'emerald')
        self.revenues_apply_button.clicked.connect(self.generate_revenues_report)

        self.revenues_export_button = QPushButton("📤 تصدير ▼")
        self.style_advanced_button(self.revenues_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        revenues_export_menu = QMenu(self)
        revenues_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_revenues_summary_to_excel())
        revenues_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.export_revenues_summary_to_pdf())
        revenues_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_revenues_summary_to_csv())
        revenues_export_menu.addAction(csv_action)

        self.revenues_export_button.setMenu(revenues_export_menu)

        self.revenues_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.revenues_refresh_button, 'modern_teal')
        self.revenues_refresh_button.clicked.connect(self.generate_revenues_report)

        buttons_layout.addWidget(self.revenues_apply_button)
        buttons_layout.addWidget(self.revenues_export_button)
        buttons_layout.addWidget(self.revenues_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن مطابق للتصميم الموحد
        results_group = QGroupBox("💰 نتائج تقرير الإيرادات")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # إضافة ملخص إحصائي متطور مطابق للتصميم الموحد
        stats_layout = QHBoxLayout()

        # عدد الإيرادات
        self.revenues_count_label = StyledLabel("عدد الإيرادات: 0", "info")
        stats_layout.addWidget(self.revenues_count_label.label)

        # إجمالي الإيرادات
        self.revenues_total_label = StyledLabel("إجمالي الإيرادات: 0.00 ج.م", "success")
        stats_layout.addWidget(self.revenues_total_label.label)

        # متوسط الإيرادات
        self.revenues_avg_label = StyledLabel("متوسط الإيرادات: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.revenues_avg_label.label)

        results_layout.addLayout(stats_layout)

        # تقرير الإيرادات مع تصميم متطور ومحسن مطابق للتصميم الموحد
        self.revenues_summary = QTextBrowser()
        self.revenues_summary.setMinimumHeight(350)
        self.revenues_summary.setStyleSheet("""
            QTextBrowser {
                border: 4px solid #000000;
                border-radius: 15px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 rgba(255, 255, 255, 0.98),
                    stop:0.2 rgba(248, 250, 252, 0.95),
                    stop:0.4 rgba(241, 245, 249, 0.92),
                    stop:0.6 rgba(226, 232, 240, 0.88),
                    stop:0.8 rgba(203, 213, 225, 0.85),
                    stop:1 rgba(241, 245, 249, 0.95));
                font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                font-size: 17px;
                font-weight: 600;
                color: #0f172a;
                padding: 30px;
                line-height: 1.9;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
                box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.08);
            }
            QScrollBar:vertical {
                border: 3px solid #000000;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #f1f5f9, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                width: 20px;
                border-radius: 10px;
                margin: 2px;
            }
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #3b82f6, stop:0.3 #2563eb, stop:0.7 #1d4ed8, stop:1 #1e40af);
                border: 2px solid #000000;
                border-radius: 8px;
                min-height: 30px;
                margin: 1px;
            }
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #60a5fa, stop:0.3 #3b82f6, stop:0.7 #2563eb, stop:1 #1d4ed8);
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.3);
            }
            QScrollBar::handle:vertical:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1d4ed8, stop:0.5 #1e40af, stop:1 #1e3a8a);
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # جدول الإيرادات مع تصميم متطور
        self.revenues_table = QTableWidget()
        self.revenues_table.setColumnCount(5)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "📅 التاريخ",
            "📝 الوصف",
            "🏷️ النوع",
            "💰 المبلغ",
            "📋 الملاحظات"
        ]
        self.revenues_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.revenues_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.revenues_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                background-clip: padding-box !important;
                box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.2),
                           0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #2563EB,
                    stop:0.3 #3B82F6, stop:0.4 #6366F1, stop:0.5 #8B5CF6,
                    stop:0.6 #A855F7, stop:0.7 #C084FC, stop:0.8 #E879F9,
                    stop:0.9 #F0ABFC, stop:1 #FBBF24) !important;
                color: #FFFFFF !important;
                text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(-2px) !important;
                box-shadow: inset 0 2px 6px rgba(255, 255, 255, 0.3),
                           0 6px 12px rgba(0, 0, 0, 0.4) !important;
            }

            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E293B, stop:0.4 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.8 #6366F1, stop:1 #7C3AED) !important;
                color: #FFFFFF !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(1px) !important;
                box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.4) !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.revenues_table.verticalHeader().setDefaultSectionSize(45)
        self.revenues_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.revenues_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.revenues_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.revenues_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.revenues_table.setAlternatingRowColors(True)

        # إخفاء شريط التمرير المرئي مع الحفاظ على التمرير بالماوس
        self.revenues_table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)

        # ضبط إعدادات التمرير للتحكم الدقيق
        try:
            scrollbar = self.revenues_table.verticalScrollBar()
            if scrollbar:
                scrollbar.setSingleStep(50)
                scrollbar.setPageStep(200)
        except Exception:
            pass

        # إضافة معالج التمرير المخصص
        def revenues_reports_wheelEvent(event):
            try:
                delta = event.angleDelta().y()
                if abs(delta) < 120:
                    event.accept()
                    return

                scrollbar = self.revenues_table.verticalScrollBar()
                if not scrollbar:
                    event.accept()
                    return

                if delta > 0:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepSub)
                else:
                    scrollbar.triggerAction(QAbstractSlider.SliderSingleStepAdd)

                event.accept()
            except Exception:
                QTableWidget.wheelEvent(self.revenues_table, event)

        self.revenues_table.wheelEvent = revenues_reports_wheelEvent

        results_layout.addWidget(self.revenues_summary)

        # تم إزالة الجدول والعنوان حسب الطلب

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)  # تقليل المسافة بين الفلاتر والنتائج
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية

        self.revenues_tab.setLayout(layout)

    def generate_revenues_report(self):
        """إنتاج تقرير الإيرادات"""
        try:
            # الحصول على الفلاتر
            start_date = qdate_to_datetime(self.revenues_start_date.date())
            end_date = qdate_to_datetime(self.revenues_end_date.date())
            category = self.revenues_category_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Revenue).filter(
                Revenue.date >= start_date,
                Revenue.date <= end_date
            )

            # تطبيق فلتر النوع
            if category:
                query = query.filter(Revenue.category == category)

            # تنفيذ الاستعلام
            revenues = query.order_by(Revenue.date.desc()).all()

            # حساب الإحصائيات المتقدمة
            total_revenues = sum(revenue.amount for revenue in revenues)

            # تجميع الإيرادات حسب الفئة
            categories_stats = {}
            for revenue in revenues:
                category = revenue.category or 'غير محدد'
                if category not in categories_stats:
                    categories_stats[category] = {'count': 0, 'total': 0}
                categories_stats[category]['count'] += 1
                categories_stats[category]['total'] += revenue.amount

            # حساب متوسط الإيراد
            avg_revenue = total_revenues / len(revenues) if revenues else 0

            # أكبر وأصغر إيراد
            max_revenue = max(revenues, key=lambda x: x.amount) if revenues else None
            min_revenue = min(revenues, key=lambda x: x.amount) if revenues else None

            # تحديث الملخص
            self.revenues_count_label.setText(f"💰 عدد الإيرادات: {len(revenues)}")
            self.revenues_total_label.setText(f"💵 إجمالي الإيرادات: {format_currency(total_revenues)}")

            # إنشاء تقرير HTML متطور ومحسن
            html_report = f"""
            <html>
            <head>
                <style>
                    body {{
                        font-family: 'Segoe UI', 'Roboto', 'Arial', 'Tahoma', sans-serif;
                        direction: rtl;
                        background: linear-gradient(135deg,
                            #ffffff 0%, #f0fdf4 15%, #dcfce7 30%,
                            #bbf7d0 45%, #86efac 60%, #4ade80 75%,
                            #22c55e 90%, #16a34a 100%);
                        margin: 0;
                        padding: 25px;
                        min-height: 100vh;
                        color: #0f172a;
                        line-height: 1.8;
                    }}
                    .header {{
                        text-align: center;
                        background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 50%, #ffffff 100%);
                        padding: 35px;
                        border-radius: 25px;
                        margin-bottom: 30px;
                        box-shadow: 0 12px 35px rgba(15, 23, 42, 0.15),
                                   0 4px 15px rgba(34, 197, 94, 0.1);
                        border: 4px solid #22c55e;
                        position: relative;
                        overflow: hidden;
                    }}
                    .header::before {{
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        height: 4px;
                        background: linear-gradient(90deg, #22c55e, #4ade80, #86efac, #bbf7d0, #dcfce7);
                    }}
                    .summary-cards {{
                        display: grid;
                        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                        gap: 20px;
                        margin: 25px 0;
                    }}
                    .summary-card {{
                        background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);
                        padding: 25px;
                        border-radius: 18px;
                        border-left: 6px solid #22c55e;
                        border-right: 2px solid #dcfce7;
                        box-shadow: 0 6px 20px rgba(34, 197, 94, 0.12),
                                   0 2px 8px rgba(15, 23, 42, 0.08);
                        text-align: center;
                        transition: transform 0.2s ease;
                    }}
                    .summary-card:hover {{
                        transform: translateY(-3px);
                        box-shadow: 0 8px 25px rgba(34, 197, 94, 0.18);
                    }}
                    .card-title {{
                        color: #1e293b;
                        font-size: 16px;
                        margin-bottom: 12px;
                        font-weight: 600;
                    }}
                    .card-value {{
                        color: #22c55e;
                        font-size: 26px;
                        font-weight: 700;
                        text-shadow: 0 1px 2px rgba(34, 197, 94, 0.2);
                    }}
                    .stats-section {{
                        background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 50%, #ffffff 100%);
                        padding: 25px;
                        border-radius: 20px;
                        margin: 25px 0;
                        box-shadow: 0 8px 25px rgba(34, 197, 94, 0.15),
                                   0 3px 12px rgba(15, 23, 42, 0.1);
                        border: 2px solid #dcfce7;
                    }}
                    .stats-section h3 {{
                        color: #22c55e;
                        font-size: 22px;
                        margin-top: 0;
                        margin-bottom: 20px;
                        display: flex;
                        align-items: center;
                        gap: 10px;
                    }}
                    .stat-item {{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        padding: 15px 0;
                        border-bottom: 2px solid #f0fdf4;
                        transition: background-color 0.2s ease;
                    }}
                    .stat-item:hover {{
                        background-color: rgba(34, 197, 94, 0.05);
                        border-radius: 8px;
                        margin: 0 -10px;
                        padding: 15px 10px;
                    }}
                    .stat-item:last-child {{
                        border-bottom: none;
                    }}
                    .stat-label {{
                        font-weight: 600;
                        color: #1e293b;
                        font-size: 16px;
                    }}
                    .stat-value {{
                        font-weight: 700;
                        font-size: 16px;
                    }}
                    .positive {{ color: #22c55e; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(34, 197, 94, 0.2); }}
                    .neutral {{ color: #22c55e; font-weight: 700; font-size: 17px; text-shadow: 0 1px 2px rgba(34, 197, 94, 0.2); }}
                    .category-item {{
                        background: rgba(34, 197, 94, 0.05);
                        padding: 12px;
                        margin: 8px 0;
                        border-radius: 10px;
                        border-left: 4px solid #22c55e;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1 style="color: #22c55e; font-size: 30px; margin: 0 0 15px 0; text-shadow: 0 2px 4px rgba(34, 197, 94, 0.3);">
                        💰 تقرير الإيرادات المتطور والشامل
                    </h1>
                    <p style="color: #64748b; font-size: 18px; margin: 10px 0; font-weight: 500;">
                        📊 تحليل تفصيلي وشامل للإيرادات والدخل
                    </p>
                    <div style="margin-top: 20px; padding: 12px; background: rgba(34, 197, 94, 0.1); border-radius: 12px;">
                        <span style="color: #22c55e; font-weight: 600; font-size: 16px;">📅 فترة التقرير: </span>
                        <span style="color: #1e293b; font-weight: 500; font-size: 16px;">من {start_date.strftime('%Y-%m-%d')} إلى {end_date.strftime('%Y-%m-%d')}</span>
                    </div>
                </div>

                <div class="summary-cards">
                    <div class="summary-card">
                        <div class="card-title">إجمالي الإيرادات</div>
                        <div class="card-value">{len(revenues)}</div>
                    </div>
                    <div class="summary-card">
                        <div class="card-title">إجمالي المبلغ</div>
                        <div class="card-value positive">{format_currency(total_revenues)}</div>
                    </div>
                    <div class="summary-card">
                        <div class="card-title">متوسط الإيراد</div>
                        <div class="card-value neutral">{format_currency(avg_revenue)}</div>
                    </div>
                    <div class="summary-card">
                        <div class="card-title">عدد الفئات</div>
                        <div class="card-value">{len(categories_stats)}</div>
                    </div>
                </div>

                <div class="stats-section">
                    <h3>📊 إحصائيات تفصيلية</h3>
                    <div class="stat-item">
                        <span class="stat-label">
                            <span style="margin-left: 8px;">🔺</span>أكبر إيراد:
                        </span>
                        <span class="stat-value positive">{format_currency(max_revenue.amount) if max_revenue else '0'} ({max_revenue.description if max_revenue else 'لا يوجد'})</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">
                            <span style="margin-left: 8px;">🔻</span>أصغر إيراد:
                        </span>
                        <span class="stat-value positive">{format_currency(min_revenue.amount) if min_revenue else '0'} ({min_revenue.description if min_revenue else 'لا يوجد'})</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">
                            <span style="margin-left: 8px;">📈</span>متوسط الإيراد اليومي:
                        </span>
                        <span class="stat-value neutral">{format_currency(total_revenues / ((end_date - start_date).days + 1)) if revenues else 0}</span>
                    </div>
                </div>

                <div class="stats-section">
                    <h3>🏷️ تحليل الإيرادات حسب الفئة</h3>
            """

            for category, stats in categories_stats.items():
                percentage = (stats['total'] / total_revenues * 100) if total_revenues > 0 else 0
                html_report += f"""
                    <div class="category-item">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <span style="font-weight: 600; color: #1e293b;">{category}</span>
                            <div style="text-align: left;">
                                <span style="color: #22c55e; font-weight: 700;">{format_currency(stats['total'])}</span>
                                <span style="color: #64748b; font-size: 14px; margin-right: 10px;">({stats['count']} إيراد - {percentage:.1f}%)</span>
                            </div>
                        </div>
                    </div>
                """

            html_report += """
                </div>
            </body>
            </html>
            """

            self.revenues_summary.setHtml(html_report)

            # تم إزالة كود تحديث الجدول

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير الإيرادات: {str(e)}")

    def setup_projects_tab(self):
        """إعداد تبويب تقرير المشاريع"""
        layout = QVBoxLayout()

        # مجموعة فلاتر التقرير مع تصميم محسن مطابق للعملاء والموردين
        filter_group = QGroupBox("🏗️ فلاتر تقرير المشاريع")
        filter_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 12px;
                padding-top: 18px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        filter_layout = QFormLayout()
        filter_layout.setSpacing(12)
        filter_layout.setContentsMargins(20, 25, 20, 15)

        # فلتر المشروع مع تصميم محسن
        project_label = QLabel("🏗️ المشروع:")
        project_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.projects_project_combo = QComboBox()
        self.projects_project_combo.addItem("جميع المشاريع", None)
        try:
            projects = self.session.query(Project).order_by(Project.name).all()
            for project in projects:
                self.projects_project_combo.addItem(project.name, project.id)
        except:
            pass
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.projects_project_combo, 'primary')
        filter_layout.addRow(project_label, self.projects_project_combo)

        # فلتر حالة المشروع مع تصميم محسن
        status_label = QLabel("📊 حالة المشروع:")
        status_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ffffff;
                padding: 8px 12px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95);
                border: 2px solid #000000;
                border-radius: 8px;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
            }
        """)

        self.projects_status_combo = QComboBox()
        self.projects_status_combo.addItem("جميع الحالات", None)
        self.projects_status_combo.addItem("قيد التخطيط", "planning")
        self.projects_status_combo.addItem("قيد التنفيذ", "in_progress")
        self.projects_status_combo.addItem("مكتمل", "completed")
        self.projects_status_combo.addItem("متوقف", "on_hold")
        self.projects_status_combo.addItem("ملغي", "cancelled")
        # تطبيق التصميم المتطور الموحد مع الألوان الجديدة
        self.style_advanced_combobox(self.projects_status_combo, 'primary')
        filter_layout.addRow(status_label, self.projects_status_combo)

        # أزرار التحكم مطابقة لباقي البرنامج
        buttons_layout = QHBoxLayout()

        self.projects_apply_button = QPushButton("🏗️ تطبيق التقرير")
        self.style_advanced_button(self.projects_apply_button, 'emerald')
        self.projects_apply_button.clicked.connect(self.generate_projects_report)

        self.projects_export_button = QPushButton("📤 تصدير")
        self.style_advanced_button(self.projects_export_button, 'info')

        # إنشاء قائمة منسدلة للتصدير
        projects_export_menu = QMenu(self)
        projects_export_menu.setStyleSheet("QMenu { background-color: white; border: 1px solid #E2E8F0; }")

        excel_action = QAction("📊 تصدير إلى Excel", self)
        excel_action.triggered.connect(lambda: self.export_to_csv(self.projects_table, "تقرير_المشاريع"))
        projects_export_menu.addAction(excel_action)

        pdf_action = QAction("📄 تصدير إلى PDF", self)
        pdf_action.triggered.connect(lambda: self.print_table(self.projects_table, "تقرير المشاريع"))
        projects_export_menu.addAction(pdf_action)

        csv_action = QAction("📋 تصدير إلى CSV", self)
        csv_action.triggered.connect(lambda: self.export_to_csv(self.projects_table, "تقرير_المشاريع"))
        projects_export_menu.addAction(csv_action)

        self.projects_export_button.setMenu(projects_export_menu)

        self.projects_refresh_button = QPushButton("🔄 تحديث")
        self.style_advanced_button(self.projects_refresh_button, 'modern_teal')
        self.projects_refresh_button.clicked.connect(self.generate_projects_report)

        buttons_layout.addWidget(self.projects_apply_button)
        buttons_layout.addWidget(self.projects_export_button)
        buttons_layout.addWidget(self.projects_refresh_button)
        buttons_layout.addStretch()

        filter_layout.addRow("", buttons_layout)

        filter_group.setLayout(filter_layout)

        # مجموعة نتائج التقرير مع تصميم محسن مطابق للعملاء والموردين
        results_group = QGroupBox("🏗️ نتائج تقرير المشاريع")
        results_group.setStyleSheet("""
            QGroupBox {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                border: 4px solid #000000;
                border-radius: 12px;
                margin-top: 2px;
                padding-top: 5px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.4);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 25px;
                padding: 8px 20px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b);
                color: #ffffff;
                border: 3px solid #000000;
                border-radius: 10px;
                font-size: 16px;
                font-weight: bold;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
        """)
        results_layout = QVBoxLayout()
        results_layout.setContentsMargins(20, 25, 20, 15)
        results_layout.setSpacing(15)

        # إضافة ملخص إحصائي متطور مطابق للعملاء والموردين
        stats_layout = QHBoxLayout()

        # عدد المشاريع
        self.projects_count_label = StyledLabel("عدد المشاريع: 0", "info")
        stats_layout.addWidget(self.projects_count_label.label)

        # إجمالي الميزانية
        self.projects_total_budget_label = StyledLabel("إجمالي الميزانية: 0.00 ج.م", "success")
        stats_layout.addWidget(self.projects_total_budget_label.label)

        # متوسط الميزانية
        self.projects_avg_budget_label = StyledLabel("متوسط الميزانية: 0.00 ج.م", "warning")
        stats_layout.addWidget(self.projects_avg_budget_label.label)

        results_layout.addLayout(stats_layout)

        # جدول المشاريع مع تصميم متطور
        self.projects_table = QTableWidget()
        self.projects_table.setColumnCount(6)
        # عناوين محسنة مع أيقونات متطورة وجذابة مطابقة للفواتير
        headers = [
            "🏗️ اسم المشروع",
            "👨‍💼 العميل",
            "🚀 تاريخ البداية",
            "🏁 تاريخ النهاية",
            "💰 الميزانية",
            "🎯 الحالة"
        ]
        self.projects_table.setHorizontalHeaderLabels(headers)

        # تطبيق تصميم متطور جداً وأنيق للجدول مطابق للتصميم الموحد
        self.projects_table.setStyleSheet("""
            QTableWidget {
                gridline-color: rgba(0, 0, 0, 0.3);
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #f8fafc, stop:0.3 #e2e8f0, stop:0.7 #cbd5e1, stop:1 #94a3b8);
                border: 4px solid #000000;
                border-radius: 15px;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                font-size: 15px;
                font-weight: bold;
                selection-background-color: rgba(37, 99, 235, 0.2);
                alternate-background-color: rgba(30, 41, 59, 0.1);
                outline: none;
                padding: 8px;
                color: #1e293b;
            }

            QTableWidget::item {
                padding: 12px 15px;
                border: 2px solid rgba(0, 0, 0, 0.2);
                text-align: center;
                min-height: 35px;
                max-height: 50px;
                font-weight: bold;
                font-size: 15px;
                border-radius: 8px;
                margin: 2px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(248, 250, 252, 0.95),
                    stop:0.5 rgba(226, 232, 240, 0.9),
                    stop:1 rgba(203, 213, 225, 0.85));
                color: #1e293b;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            QTableWidget::item:selected {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #1e293b, stop:0.2 #0f172a, stop:0.3 #020617,
                    stop:0.4 #1a365d, stop:0.6 #1d4ed8, stop:0.7 #020617,
                    stop:0.8 #0f172a, stop:1 #1e293b) !important;
                color: #ffffff !important;
                border: 3px solid #2563EB !important;
                border-radius: 10px !important;
                font-weight: bold !important;
                font-size: 16px !important;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6) !important;
                box-shadow: 0 3px 8px rgba(37, 99, 235, 0.3) !important;
            }

            QTableWidget::item:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569) !important;
                border: 3px solid #3B82F6 !important;
                border-radius: 10px !important;
                color: #ffffff !important;
                font-weight: bold !important;
                font-size: 15px !important;
                text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4) !important;
                box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2) !important;
            }

            QHeaderView::section {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                color: #ffffff;
                padding: 15px 20px;
                margin: 0px;
                font-weight: bold;
                font-size: 16px;
                font-family: 'Segoe UI', 'Roboto', 'Arial', sans-serif;
                border: 3px solid #000000;
                border-bottom: 4px solid #000000;
                text-align: center;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
            }
            QHeaderView::section:hover {
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #475569, stop:0.2 #334155, stop:0.3 #1e293b,
                    stop:0.4 #1d4ed8, stop:0.6 #2563EB, stop:0.7 #1e293b,
                    stop:0.8 #334155, stop:1 #475569);
                color: #ffffff;
            }
        """)

        # تطبيق تنسيق العناوين المتطور مطابق للفواتير والمصروفات والإيرادات
        header = self.projects_table.horizontalHeader()
        header.setStyleSheet("")  # إزالة أي تنسيق سابق

        # ألوان مشابهة للعنوان الرئيسي لكن مطورة وهادئة مع خط بولد قوي
        new_header_style = """
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.1 #1E293B, stop:0.2 #1E40AF,
                    stop:0.3 #1D4ED8, stop:0.4 #2563EB, stop:0.5 #3B82F6,
                    stop:0.6 #6366F1, stop:0.7 #7C3AED, stop:0.8 #6D28D9,
                    stop:0.9 #5B21B6, stop:1 #4C1D95) !important;
                color: #FFFFFF !important;
                padding: 12px 16px !important;
                margin: 0px !important;
                font-weight: 900 !important;
                font-size: 16px !important;
                font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif !important;
                border: 3px solid rgba(255, 255, 255, 0.6) !important;
                border-bottom: 4px solid rgba(255, 255, 255, 0.8) !important;
                border-left: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-right: 2px solid rgba(255, 255, 255, 0.4) !important;
                border-radius: 12px 12px 0 0 !important;
                text-align: center !important;
                text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8) !important;
                background-clip: padding-box !important;
                box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.2),
                           0 4px 8px rgba(0, 0, 0, 0.3) !important;
            }

            QHeaderView::section:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #1E293B, stop:0.1 #334155, stop:0.2 #2563EB,
                    stop:0.3 #3B82F6, stop:0.4 #6366F1, stop:0.5 #8B5CF6,
                    stop:0.6 #A855F7, stop:0.7 #C084FC, stop:0.8 #E879F9,
                    stop:0.9 #F0ABFC, stop:1 #FBBF24) !important;
                color: #FFFFFF !important;
                text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(-2px) !important;
                box-shadow: inset 0 2px 6px rgba(255, 255, 255, 0.3),
                           0 6px 12px rgba(0, 0, 0, 0.4) !important;
            }

            QHeaderView::section:pressed {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #0F172A, stop:0.2 #1E293B, stop:0.4 #1D4ED8,
                    stop:0.6 #2563EB, stop:0.8 #6366F1, stop:1 #7C3AED) !important;
                color: #FFFFFF !important;
                text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.9) !important;
                transform: translateY(1px) !important;
                box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.4) !important;
            }
        """

        # تطبيق التنسيق الجديد
        header.setStyleSheet(new_header_style)

        # تحسين ارتفاع الصفوف مطابق للفواتير والمصروفات والإيرادات
        self.projects_table.verticalHeader().setDefaultSectionSize(45)
        self.projects_table.verticalHeader().setVisible(False)

        # تحسين رأس الجدول بشكل احترافي مع تأثيرات
        header.setFixedHeight(55)
        header.setDefaultAlignment(Qt.AlignCenter)
        header.setMinimumSectionSize(120)

        self.projects_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.projects_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.projects_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.projects_table.setAlternatingRowColors(True)

        # عنوان الجدول مع تصميم محسن مطابق للتصميم الموحد
        table_title = QLabel("📊 تفاصيل المشاريع")
        table_title.setStyleSheet("""
            QLabel {
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                padding: 15px 25px;
                background: qlineargradient(x1:1, y1:0, x2:0, y2:0,
                    stop:0 #334155, stop:0.15 #1e293b, stop:0.25 #0f172a,
                    stop:0.3 #1a365d, stop:0.4 #1d4ed8, stop:0.5 #2563EB,
                    stop:0.6 #1a365d, stop:0.75 #0f172a, stop:0.85 #1e293b,
                    stop:1 #334155);
                border: 4px solid #000000;
                border-radius: 12px;
                margin: 8px 0;
                text-shadow: 0 2px 4px rgba(0, 0, 0, 0.6);
                box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            }
        """)
        results_layout.addWidget(table_title)
        results_layout.addWidget(self.projects_table)

        results_group.setLayout(results_layout)

        layout.addWidget(filter_group)
        layout.addWidget(results_group)

        # تقليل المساحات بين العناصر
        layout.setSpacing(2)  # تقليل المسافة بين الفلاتر والنتائج
        layout.setContentsMargins(2, 2, 2, 2)  # تقليل الهوامش الخارجية

        self.projects_tab.setLayout(layout)

    def generate_projects_report(self):
        """إنتاج تقرير المشاريع"""
        try:
            # الحصول على الفلاتر
            project_id = self.projects_project_combo.currentData()
            status = self.projects_status_combo.currentData()

            # بناء الاستعلام
            query = self.session.query(Project)

            # تطبيق فلتر المشروع
            if project_id:
                query = query.filter(Project.id == project_id)

            # تطبيق فلتر الحالة
            if status:
                query = query.filter(Project.status == status)

            # تنفيذ الاستعلام
            projects = query.order_by(Project.name).all()

            # حساب الإحصائيات
            total_budget = sum(project.budget or 0 for project in projects)

            # تحديث الملخص
            self.projects_count_label.setText(f"🏗️ عدد المشاريع: {len(projects)}")
            self.projects_budget_label.setText(f"💰 إجمالي الميزانية: {format_currency(total_budget)}")

            # تحديث الجدول
            self.projects_table.setRowCount(len(projects))
            for row, project in enumerate(projects):
                # اسم المشروع
                self.projects_table.setItem(row, 0, QTableWidgetItem(project.name or ""))

                # العميل
                client_name = ""
                if project.client_id:
                    try:
                        client = self.session.query(Client).get(project.client_id)
                        if client:
                            client_name = client.name
                    except:
                        pass
                self.projects_table.setItem(row, 1, QTableWidgetItem(client_name))

                # تاريخ البداية
                start_date_str = project.start_date.strftime("%Y-%m-%d") if project.start_date else ""
                self.projects_table.setItem(row, 2, QTableWidgetItem(start_date_str))

                # تاريخ النهاية
                end_date_str = project.end_date.strftime("%Y-%m-%d") if project.end_date else ""
                self.projects_table.setItem(row, 3, QTableWidgetItem(end_date_str))

                # الميزانية
                self.projects_table.setItem(row, 4, QTableWidgetItem(format_currency(project.budget or 0)))

                # الحالة
                statuses = {
                    'planning': 'قيد التخطيط',
                    'in_progress': 'قيد التنفيذ',
                    'completed': 'مكتمل',
                    'on_hold': 'متوقف',
                    'cancelled': 'ملغي'
                }
                status_text = statuses.get(project.status, project.status or "")
                self.projects_table.setItem(row, 5, QTableWidgetItem(status_text))

        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء إنتاج تقرير المشاريع: {str(e)}")

    def export_suppliers_to_pdf(self):
        """تصدير تقرير الموردين إلى PDF"""
        try:
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
            from PyQt5.QtWidgets import QTextDocument

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                # الحصول على محتوى التقرير
                html_content = self.suppliers_summary.toHtml()

                # إنشاء مستند للطباعة
                document = QTextDocument()
                document.setHtml(html_content)
                document.print_(printer)

                QMessageBox.information(self, "نجحت الطباعة", "تم تصدير تقرير الموردين بنجاح")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير تقرير الموردين: {str(e)}")

    def save_suppliers_html(self):
        """حفظ تقرير الموردين كملف HTML"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ تقرير الموردين",
                "تقرير_الموردين.html",
                "HTML Files (*.html)"
            )

            if file_path:
                html_content = self.suppliers_summary.toHtml()
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                QMessageBox.information(self, "نجح الحفظ", f"تم حفظ تقرير الموردين بنجاح في:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في الحفظ", f"حدث خطأ أثناء حفظ تقرير الموردين: {str(e)}")

    def export_to_csv(self, table, filename):
        """تصدير جدول إلى ملف CSV"""
        try:
            # فتح حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{filename}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(table.columnCount()):
                        headers.append(table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def print_table(self, table, title):
        """طباعة جدول"""
        try:
            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                # إنشاء HTML للطباعة
                html = f"""
                <html>
                <head>
                    <meta charset="utf-8">
                    <style>
                        body {{ font-family: Arial, sans-serif; direction: rtl; }}
                        h1 {{ text-align: center; color: #2c3e50; }}
                        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                        th, td {{ border: 1px solid #bdc3c7; padding: 8px; text-align: center; }}
                        th {{ background-color: #34495e; color: white; }}
                        tr:nth-child(even) {{ background-color: #f8f9fa; }}
                    </style>
                </head>
                <body>
                    <h1>{title}</h1>
                    <table>
                        <tr>
                """

                # إضافة رؤوس الأعمدة
                for col in range(table.columnCount()):
                    header = table.horizontalHeaderItem(col).text()
                    html += f"<th>{header}</th>"
                html += "</tr>"

                # إضافة البيانات
                for row in range(table.rowCount()):
                    html += "<tr>"
                    for col in range(table.columnCount()):
                        item = table.item(row, col)
                        cell_text = item.text() if item else ""
                        html += f"<td>{cell_text}</td>"
                    html += "</tr>"

                html += """
                    </table>
                </body>
                </html>
                """

                # طباعة HTML
                from PyQt5.QtWidgets import QTextDocument
                document = QTextDocument()
                document.setHtml(html)
                document.print_(printer)

                QMessageBox.information(self, "نجحت الطباعة", "تم إرسال التقرير للطباعة بنجاح")

        except Exception as e:
            show_error_message("خطأ في الطباعة", f"حدث خطأ أثناء طباعة التقرير: {str(e)}")

    def style_advanced_combobox(self, combobox, color_type='primary'):
        """تطبيق تصميم متطور وموحد على القوائم المنسدلة مطابق للفواتير والمصروفات والإيرادات"""
        try:
            # تحديد الألوان حسب النوع
            color_schemes = {
                'primary': {
                    'border': '#4f46e5', 'hover_border': '#6366f1', 'focus_border': '#7c3aed',
                    'bg': '#ffffff', 'hover_bg': '#f8fafc', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#4f46e5'
                },
                'emerald': {
                    'border': '#10b981', 'hover_border': '#14b8a6', 'focus_border': '#0d9488',
                    'bg': '#ffffff', 'hover_bg': '#f0fdfa', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#10b981'
                },
                'danger': {
                    'border': '#dc2626', 'hover_border': '#ef4444', 'focus_border': '#f87171',
                    'bg': '#ffffff', 'hover_bg': '#fef2f2', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#dc2626'
                },
                'info': {
                    'border': '#0ea5e9', 'hover_border': '#38bdf8', 'focus_border': '#7dd3fc',
                    'bg': '#ffffff', 'hover_bg': '#f0f9ff', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#0ea5e9'
                },
                'warning': {
                    'border': '#f59e0b', 'hover_border': '#fbbf24', 'focus_border': '#fcd34d',
                    'bg': '#ffffff', 'hover_bg': '#fffbeb', 'focus_bg': '#ffffff',
                    'text': '#1e293b', 'arrow': '#f59e0b'
                }
            }

            colors = color_schemes.get(color_type, color_schemes['primary'])

            style = f"""
                QComboBox {{
                    background: {colors['bg']};
                    border: 3px solid {colors['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-size: 14px;
                    font-weight: 600;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    color: {colors['text']};
                    min-height: 35px;
                    max-height: 35px;
                    text-align: center;
                    selection-background-color: rgba(79, 70, 229, 0.2);
                    selection-color: #ffffff;
                }}
                QComboBox:hover {{
                    background: {colors['hover_bg']};
                    border: 4px solid {colors['hover_border']};
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }}
                QComboBox:focus {{
                    background: {colors['focus_bg']};
                    border: 4px solid {colors['focus_border']};
                    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
                }}
                QComboBox::drop-down {{
                    border: none;
                    width: 35px;
                    border-radius: 0 16px 16px 0;
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 rgba(79, 70, 229, 0.1),
                        stop:1 rgba(79, 70, 229, 0.2));
                }}
                QComboBox::down-arrow {{
                    image: none;
                    border: 6px solid transparent;
                    border-top: 10px solid {colors['arrow']};
                    margin-right: 8px;
                    margin-top: 2px;
                }}
                QComboBox::down-arrow:hover {{
                    border-top: 10px solid {colors['hover_border']};
                }}
                QComboBox QAbstractItemView {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ffffff, stop:0.2 #f8fafc, stop:0.5 #f1f5f9,
                        stop:0.8 #e2e8f0, stop:1 #cbd5e1);
                    border: none;
                    border-radius: 12px;
                    selection-background-color: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3B82F6, stop:0.3 #6366F1, stop:0.6 #8B5CF6, stop:1 #A855F7);
                    selection-color: #ffffff;
                    font-size: 16px;
                    font-weight: 600;
                    color: #1e293b;
                    padding: 8px;
                    outline: none;
                    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
                }}
                QComboBox QAbstractItemView::item {{
                    padding: 10px 15px;
                    border-radius: 8px;
                    margin: 2px;
                    color: #1e293b;
                    font-size: 16px;
                    font-weight: 600;
                    background: transparent;
                }}
                QComboBox QAbstractItemView::item:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #ddd6fe, stop:0.3 #c4b5fd, stop:0.7 #a78bfa, stop:1 #8b5cf6);
                    color: #ffffff;
                    font-weight: 700;
                    font-size: 16px;
                    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                    border-radius: 10px;
                }}
                QComboBox QAbstractItemView::item:selected {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                        stop:0 #3B82F6, stop:0.3 #6366F1, stop:0.6 #8B5CF6, stop:1 #A855F7);
                    color: #ffffff;
                    font-weight: 800;
                    font-size: 16px;
                    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    border-radius: 10px;
                }}
            """

            combobox.setStyleSheet(style)

        except Exception as e:
            print(f"خطأ في تطبيق تصميم ComboBox: {e}")

    def apply_unified_combobox_styles(self):
        """تطبيق التصميم الموحد على جميع القوائم المنسدلة في التقارير"""
        try:
            # قوائم المشتريات
            if hasattr(self, 'purchases_period_combo'):
                self.style_advanced_combobox(self.purchases_period_combo, 'danger')
            if hasattr(self, 'purchases_supplier_combo'):
                self.style_advanced_combobox(self.purchases_supplier_combo, 'emerald')
            if hasattr(self, 'purchases_status_combo'):
                self.style_advanced_combobox(self.purchases_status_combo, 'warning')

            # قوائم المخزون
            if hasattr(self, 'inventory_category_combo'):
                self.style_advanced_combobox(self.inventory_category_combo, 'primary')
            if hasattr(self, 'inventory_supplier_combo'):
                self.style_advanced_combobox(self.inventory_supplier_combo, 'emerald')
            if hasattr(self, 'inventory_status_combo'):
                self.style_advanced_combobox(self.inventory_status_combo, 'danger')

            # قوائم العملاء
            if hasattr(self, 'clients_client_combo'):
                self.style_advanced_combobox(self.clients_client_combo, 'emerald')

            # قوائم الموردين
            if hasattr(self, 'suppliers_supplier_combo'):
                self.style_advanced_combobox(self.suppliers_supplier_combo, 'emerald')

            # قوائم المصروفات
            if hasattr(self, 'expenses_period_combo'):
                self.style_advanced_combobox(self.expenses_period_combo, 'danger')
            if hasattr(self, 'expenses_category_combo'):
                self.style_advanced_combobox(self.expenses_category_combo, 'warning')

            # قوائم الإيرادات
            if hasattr(self, 'revenues_period_combo'):
                self.style_advanced_combobox(self.revenues_period_combo, 'emerald')
            if hasattr(self, 'revenues_category_combo'):
                self.style_advanced_combobox(self.revenues_category_combo, 'warning')

            # قوائم المشاريع
            if hasattr(self, 'projects_project_combo'):
                self.style_advanced_combobox(self.projects_project_combo, 'primary')
            if hasattr(self, 'projects_status_combo'):
                self.style_advanced_combobox(self.projects_status_combo, 'warning')

            # قوائم الأرباح والخسائر
            if hasattr(self, 'profit_loss_period_combo'):
                self.style_advanced_combobox(self.profit_loss_period_combo, 'warning')

        except Exception as e:
            print(f"خطأ في تطبيق التصميم الموحد للقوائم المنسدلة: {e}")

    def style_advanced_button(self, button, button_type, has_menu=False):
        """تطبيق تصميم متطور وجذاب على الأزرار مع ألوان متنوعة ومميزة"""
        try:
            # تحديد الألوان المتنوعة والمميزة حسب نوع الزر مطابق تماماً للفواتير
            colors = {
                'primary': {
                    'bg_start': '#1a1a2e', 'bg_mid': '#16213e', 'bg_end': '#0f3460', 'bg_bottom': '#533483',
                    'hover_start': '#2a2a3e', 'hover_mid': '#26314e', 'hover_end': '#1f4470', 'hover_bottom': '#634493',
                    'hover_border': '#4f46e5', 'pressed_start': '#0a0a1e', 'pressed_mid': '#06112e',
                    'pressed_end': '#052450', 'pressed_bottom': '#332473', 'pressed_border': '#3730a3',
                    'border': '#4f46e5', 'text': '#ffffff', 'shadow': 'rgba(79, 70, 229, 0.5)'
                },
                'emerald': {
                    'bg_start': '#064e3b', 'bg_mid': '#047857', 'bg_end': '#065f46', 'bg_bottom': '#10b981',
                    'hover_start': '#10b981', 'hover_mid': '#34d399', 'hover_end': '#6ee7b7', 'hover_bottom': '#a7f3d0',
                    'hover_border': '#10b981', 'pressed_start': '#022c22', 'pressed_mid': '#064e3b',
                    'pressed_end': '#014737', 'pressed_bottom': '#047857', 'pressed_border': '#065f46',
                    'border': '#10b981', 'text': '#ffffff', 'shadow': 'rgba(16, 185, 129, 0.6)'
                },
                'danger': {
                    'bg_start': '#7f1d1d', 'bg_mid': '#991b1b', 'bg_end': '#b91c1c', 'bg_bottom': '#dc2626',
                    'hover_start': '#dc2626', 'hover_mid': '#ef4444', 'hover_end': '#f87171', 'hover_bottom': '#fca5a5',
                    'hover_border': '#dc2626', 'pressed_start': '#450a0a', 'pressed_mid': '#7f1d1d',
                    'pressed_end': '#991b1b', 'pressed_bottom': '#b91c1c', 'pressed_border': '#991b1b',
                    'border': '#dc2626', 'text': '#ffffff', 'shadow': 'rgba(220, 38, 38, 0.6)'
                },
                'info': {
                    'bg_start': '#0c4a6e', 'bg_mid': '#075985', 'bg_end': '#0369a1', 'bg_bottom': '#0284c7',
                    'hover_start': '#0284c7', 'hover_mid': '#0ea5e9', 'hover_end': '#38bdf8', 'hover_bottom': '#7dd3fc',
                    'hover_border': '#0ea5e9', 'pressed_start': '#082f49', 'pressed_mid': '#0c4a6e',
                    'pressed_end': '#075985', 'pressed_bottom': '#0369a1', 'pressed_border': '#075985',
                    'border': '#0ea5e9', 'text': '#ffffff', 'shadow': 'rgba(14, 165, 233, 0.6)'
                },
                'modern_teal': {
                    'bg_start': '#042f2e', 'bg_mid': '#134e4a', 'bg_end': '#0f766e', 'bg_bottom': '#0d9488',
                    'hover_start': '#0d9488', 'hover_mid': '#14b8a6', 'hover_end': '#2dd4bf', 'hover_bottom': '#5eead4',
                    'hover_border': '#14b8a6', 'pressed_start': '#022c22', 'pressed_mid': '#042f2e',
                    'pressed_end': '#134e4a', 'pressed_bottom': '#0f766e', 'pressed_border': '#134e4a',
                    'border': '#14b8a6', 'text': '#ffffff', 'shadow': 'rgba(20, 184, 166, 0.6)'
                },
                'indigo': {
                    'bg_start': '#1e1b4b', 'bg_mid': '#312e81', 'bg_end': '#3730a3', 'bg_bottom': '#4338ca',
                    'hover_start': '#4338ca', 'hover_mid': '#6366f1', 'hover_end': '#818cf8', 'hover_bottom': '#a5b4fc',
                    'hover_border': '#6366f1', 'pressed_start': '#1e1b4b', 'pressed_mid': '#312e81',
                    'pressed_end': '#3730a3', 'pressed_bottom': '#4338ca', 'pressed_border': '#3730a3',
                    'border': '#6366f1', 'text': '#ffffff', 'shadow': 'rgba(99, 102, 241, 0.6)'
                },
                'cyan': {
                    'bg_start': '#083344', 'bg_mid': '#164e63', 'bg_end': '#0e7490', 'bg_bottom': '#0891b2',
                    'hover_start': '#0891b2', 'hover_mid': '#06b6d4', 'hover_end': '#22d3ee', 'hover_bottom': '#67e8f9',
                    'hover_border': '#06b6d4', 'pressed_start': '#083344', 'pressed_mid': '#164e63',
                    'pressed_end': '#0e7490', 'pressed_bottom': '#0891b2', 'pressed_border': '#0e7490',
                    'border': '#06b6d4', 'text': '#ffffff', 'shadow': 'rgba(6, 182, 212, 0.6)'
                },
                'rose': {
                    'bg_start': '#500724', 'bg_mid': '#831843', 'bg_end': '#9d174d', 'bg_bottom': '#be185d',
                    'hover_start': '#be185d', 'hover_mid': '#ec4899', 'hover_end': '#f472b6', 'hover_bottom': '#f9a8d4',
                    'hover_border': '#ec4899', 'pressed_start': '#500724', 'pressed_mid': '#831843',
                    'pressed_end': '#9d174d', 'pressed_bottom': '#be185d', 'pressed_border': '#9d174d',
                    'border': '#ec4899', 'text': '#ffffff', 'shadow': 'rgba(236, 72, 153, 0.6)'
                },
                'orange': {
                    'bg_start': '#431407', 'bg_mid': '#7c2d12', 'bg_end': '#9a3412', 'bg_bottom': '#c2410c',
                    'hover_start': '#c2410c', 'hover_mid': '#ea580c', 'hover_end': '#f97316', 'hover_bottom': '#fb923c',
                    'hover_border': '#ea580c', 'pressed_start': '#431407', 'pressed_mid': '#7c2d12',
                    'pressed_end': '#9a3412', 'pressed_bottom': '#c2410c', 'pressed_border': '#9a3412',
                    'border': '#ea580c', 'text': '#ffffff', 'shadow': 'rgba(234, 88, 12, 0.6)'
                },
                'warning': {
                    'bg_start': '#451a03', 'bg_mid': '#78350f', 'bg_end': '#92400e', 'bg_bottom': '#b45309',
                    'hover_start': '#b45309', 'hover_mid': '#d97706', 'hover_end': '#f59e0b', 'hover_bottom': '#fbbf24',
                    'hover_border': '#d97706', 'pressed_start': '#451a03', 'pressed_mid': '#78350f',
                    'pressed_end': '#92400e', 'pressed_bottom': '#b45309', 'pressed_border': '#92400e',
                    'border': '#d97706', 'text': '#ffffff', 'shadow': 'rgba(217, 119, 6, 0.6)'
                }
            }

            # الحصول على ألوان الزر
            color_scheme = colors.get(button_type, colors['primary'])

            # تحديد نمط القائمة المنسدلة إذا كان الزر يحتوي على قائمة
            menu_indicator = "::menu-indicator { width: 0px; }" if not has_menu else ""

            # تطبيق التصميم المتطور مطابق تماماً للفواتير والمصروفات والإيرادات
            style = f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['bg_start']},
                        stop:0.15 {color_scheme['bg_mid']},
                        stop:0.85 {color_scheme['bg_end']},
                        stop:1 {color_scheme['bg_bottom']});
                    color: {color_scheme['text']};
                    border: 4px solid {color_scheme['border']};
                    border-radius: 16px;
                    padding: 8px 16px;
                    font-weight: 900;
                    font-size: 13px;
                    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
                    min-height: 38px;
                    max-height: 38px;
                    min-width: 100px;
                    text-align: center;
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8),
                               1px 1px 2px rgba(0, 0, 0, 0.6);
                    box-shadow: 0 6px 15px {color_scheme['shadow']},
                               inset 0 2px 0 rgba(255, 255, 255, 0.3),
                               inset 0 -2px 0 rgba(0, 0, 0, 0.3),
                               0 0 20px {color_scheme['shadow']};
                    letter-spacing: 1px;
                    text-transform: uppercase;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['hover_start']},
                        stop:0.15 {color_scheme['hover_mid']},
                        stop:0.85 {color_scheme['hover_end']},
                        stop:1 {color_scheme['hover_bottom']});
                    border: 5px solid {color_scheme['hover_border']};
                    transform: translateY(-3px) scale(1.02);
                    box-shadow: 0 10px 25px {color_scheme['shadow']},
                               inset 0 3px 0 rgba(255, 255, 255, 0.4),
                               inset 0 -3px 0 rgba(0, 0, 0, 0.4),
                               0 0 30px {color_scheme['shadow']};
                    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9),
                               1px 1px 3px rgba(0, 0, 0, 0.7);
                    letter-spacing: 1.2px;
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 {color_scheme['pressed_start']},
                        stop:0.15 {color_scheme['pressed_mid']},
                        stop:0.85 {color_scheme['pressed_end']},
                        stop:1 {color_scheme['pressed_bottom']});
                    border: 4px solid {color_scheme['pressed_border']};
                    transform: translateY(1px) scale(0.98);
                    box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.6),
                               0 3px 6px {color_scheme['shadow']},
                               inset 0 0 15px rgba(0, 0, 0, 0.4);
                    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1.0),
                               1px 1px 2px rgba(0, 0, 0, 0.8);
                    letter-spacing: 0.8px;
                }}
                QPushButton:disabled {{
                    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                        stop:0 #9ca3af, stop:0.5 #6b7280, stop:1 #4b5563);
                    color: #d1d5db;
                    border: 3px solid #6b7280;
                    box-shadow: none;
                    text-shadow: none;
                    text-transform: none;
                }}
                {menu_indicator}
            """

            button.setStyleSheet(style)
            print(f"✅ تم تطبيق التصميم المتطور على الزر: {button.text()} - نوع: {button_type}")

        except Exception as e:
            print(f"❌ خطأ في تطبيق تصميم الزر: {str(e)}")



    def export_to_csv(self, table, filename):
        """تصدير جدول إلى ملف CSV"""
        try:
            # فتح حوار حفظ الملف
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                f"{filename}.csv",
                "CSV Files (*.csv)"
            )

            if file_path:
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # كتابة رؤوس الأعمدة
                    headers = []
                    for col in range(table.columnCount()):
                        headers.append(table.horizontalHeaderItem(col).text())
                    writer.writerow(headers)

                    # كتابة البيانات
                    for row in range(table.rowCount()):
                        row_data = []
                        for col in range(table.columnCount()):
                            item = table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                QMessageBox.information(self, "نجح التصدير", f"تم تصدير التقرير بنجاح إلى:\n{file_path}")

        except Exception as e:
            show_error_message("خطأ في التصدير", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def refresh_all_reports(self):
        """تحديث جميع التقارير"""
        try:
            # تحديث تقرير المبيعات
            self.generate_sales_report()
            # تحديث تقرير المشتريات
            self.generate_purchases_report()
            # تحديث تقرير الأرباح والخسائر
            self.generate_profit_loss_report()
            # تحديث تقرير المخزون
            self.generate_inventory_report()
            # تحديث تقرير العملاء
            self.generate_clients_report()

            QMessageBox.information(self, "تم التحديث", "تم تحديث جميع التقارير بنجاح")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تحديث التقارير: {str(e)}")

    def export_profit_loss_to_pdf(self):
        """تصدير تقرير الأرباح والخسائر إلى PDF"""
        try:
            from PyQt5.QtWidgets import QTextDocument
            from PyQt5.QtPrintSupport import QPrinter, QPrintDialog

            printer = QPrinter()
            print_dialog = QPrintDialog(printer, self)

            if print_dialog.exec_() == QPrintDialog.Accepted:
                document = QTextDocument()
                document.setHtml(self.profit_loss_summary.toHtml())
                document.print_(printer)

                QMessageBox.information(self, "نجحت الطباعة", "تم تصدير تقرير الأرباح والخسائر إلى PDF بنجاح")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء تصدير التقرير: {str(e)}")

    def save_profit_loss_html(self):
        """حفظ تقرير الأرباح والخسائر كملف HTML"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ التقرير",
                "تقرير_الأرباح_والخسائر.html",
                "HTML Files (*.html)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.profit_loss_summary.toHtml())

                QMessageBox.information(self, "تم الحفظ", f"تم حفظ التقرير بنجاح في:\n{file_path}")
        except Exception as e:
            show_error_message("خطأ", f"حدث خطأ أثناء حفظ التقرير: {str(e)}")
